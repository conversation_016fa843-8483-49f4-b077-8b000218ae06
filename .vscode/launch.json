{"version": "0.2.0", "configurations": [{"name": "Debug Main Process", "type": "node", "request": "launch", "cwd": "${workspaceFolder}", "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron", "runtimeArgs": [".", "--remote-debugging-port=9222"], "windows": {"runtimeExecutable": "${workspaceFolder}/node_modules/.bin/electron.cmd"}, "env": {"IS_DEV": "true"}}, {"name": "Jest Current File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["${fileBasenameNoExtension}", "--runInBand"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "windows": {"program": "${workspaceFolder}/node_modules/jest/bin/jest.js"}}]}