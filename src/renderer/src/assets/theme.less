// Dark theme variables
.theme-dark {
  --bg-color: #141414;
  --bg-color-secondary: #1e1e1e;
  --bg-color-tertiary: #252525;
  --bg-color-quaternary: #3a3a3a;
  --bg-color-quinary: #2a2a2a;
  --bg-color-senary: #1a1a1a;
  --bg-color-septenary: #282c34;
  --bg-color-octonary: #4a4a4a;
  --bg-color-novenary: #5a5a5a;
  --bg-color-suggestion: #4a6b8a;
  --bg-color-vim-editor: #2c2c2c;
  --text-color: #e0e0e0;
  --text-color-secondary: rgba(255, 255, 255, 0.85);
  --text-color-secondary-light: rgba(255, 255, 255, 0.65);
  --text-color-tertiary: rgba(255, 255, 255, 0.45);
  --text-color-quaternary: rgba(255, 255, 255, 0.25);
  --text-color-quinary: rgba(255, 255, 255, 0.15);
  --text-color-senary: rgba(255, 255, 255, 0.1);
  --text-color-septenary: rgba(255, 255, 255, 0.05);
  --border-color: #1e1e1e;
  --border-color-light: rgb(65, 65, 65);
  --hover-bg-color: rgba(255, 255, 255, 0.1);
  --active-bg-color: rgba(255, 255, 255, 0.15);
  --watermark-color: rgba(255, 255, 255, 0.05);
  --icon-filter: brightness(0) invert(1);
  --globalInput-bg-color: #1f1f1f;
  --button-bg-color: #1890ff;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  --button-default-bg-color: #3d3d3d;
  --bg-color-default: #3b3b3b;
}

// Light theme variables
.theme-light {
  --bg-color: #ffffff;
  --bg-color-secondary: #f5f5f5;
  --bg-color-tertiary: #fafafa;
  --bg-color-quaternary: #f4f4f4;
  --bg-color-quinary: #f6f6f6;
  --bg-color-senary: #f0f0f0;
  --bg-color-septenary: #d9e1ff;
  --bg-color-octonary: #f4f4f4;
  --bg-color-novenary: #f5f5f5;
  --bg-color-suggestion: #cdeff3;
  --bg-color-vim-editor: #f5f5f5;
  --text-color: #000000;
  --text-color-secondary: rgba(0, 0, 0, 0.85);
  --text-color-secondary-light: rgba(0, 0, 0, 0.65);
  --text-color-tertiary: rgba(0, 0, 0, 0.45);
  --text-color-quaternary: rgba(0, 0, 0, 0.25);
  --text-color-quinary: rgba(0, 0, 0, 0.15);
  --text-color-senary: rgba(0, 0, 0, 0.1);
  --text-color-septenary: rgba(0, 0, 0, 0.05);
  --border-color: #f5f5f5;
  --border-color-light: #e8e8e8;
  --hover-bg-color: rgba(0, 0, 0, 0.08);
  --active-bg-color: rgba(0, 0, 0, 0.12);
  --watermark-color: rgba(0, 0, 0, 0.05);
  --icon-filter: brightness(0);
  --globalInput-bg-color: #f0f0f0;
  --button-bg-color: #acd1f2;
  --box-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
  --button-default-bg-color: #ffffff;
  --bg-color-default: #d9d9d9;
}

// Apply theme variables to components
body {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

// 确保所有使用主题变量的元素都有过渡效果
* {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

.terminal-layout {
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.term_left_tab {
  background-color: var(--bg-color-secondary) !important;
  border-right-color: var(--border-color-light) !important;
}

.userInfo-container {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
}

.custom-form {
  color: var(--text-color) !important;
}

.custom-form :deep(.ant-form-item-label > label) {
  color: var(--text-color) !important;
}

// Ant Design 组件主题适配
:deep(.ant-radio-wrapper),
:deep(.ant-checkbox-wrapper),
:deep(.ant-form-item-label label),
:deep(.ant-select),
:deep(.ant-input),
:deep(.ant-input-password),
:deep(.ant-select-selection-placeholder) {
  color: var(--text-color-secondary) !important;
}

:deep(.ant-radio-inner),
:deep(.ant-checkbox-inner) {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

:deep(.ant-radio-checked .ant-radio-inner),
:deep(.ant-checkbox-checked .ant-checkbox-inner) {
  border-color: #1890ff !important;
}

:deep(.ant-select-selector),
:deep(.ant-input),
:deep(.ant-input-password) {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

.ant-input-group-wrapper {
  background-color: var(--bg-color-secondary) !important;

  .ant-input {
    background-color: var(--bg-color-secondary) !important;
    border: none;
    color: var(--text-color) !important;
  }

  .ant-input-group-addon {
    background-color: var(--bg-color-secondary) !important;
    border: none;
    color: var(--text-color) !important;

    button {
      background-color: var(--bg-color-secondary) !important;
      border: none;
      color: var(--text-color) !important;
    }
  }
}

.rigth-sidebar {
  background: var(--bg-color-tertiary) !important;
}

.splitpanes__splitter {
  background-color: var(--bg-color-secondary) !important;
}

.splitpanes__splitter:before {
  background-color: var(--hover-bg-color) !important;
}

// 菜单和标签页样式
:deep(.ant-tabs-tab),
:deep(.ant-menu-item) {
  color: var(--text-color-secondary) !important;
}

:deep(.ant-tabs-tab.ant-tabs-tab-active),
:deep(.ant-menu-item-selected) {
  color: #1890ff !important;
}

:deep(.ant-tabs-ink-bar) {
  background: #1890ff !important;
}

:deep(.ant-tabs-nav::before) {
  border-bottom-color: var(--border-color) !important;
}

// 卡片样式
:deep(.ant-card) {
  background-color: var(--bg-color) !important;
  border-color: var(--border-color) !important;
}

// 按钮样式
:deep(.ant-btn) {
  color: var(--text-color-secondary) !important;
  border-color: var(--border-color) !important;
  background: var(--bg-color) !important;

  &:hover {
    color: #1890ff !important;
    border-color: #1890ff !important;
  }
}

:deep(.ant-btn-primary) {
  color: #fff !important;
  background: #1890ff !important;
  border-color: #1890ff !important;

  &:hover {
    background: #40a9ff !important;
    border-color: #40a9ff !important;
  }
}

// 下拉菜单样式
:deep(.ant-dropdown-menu) {
  background-color: var(--bg-color) !important;
  border: 1px solid var(--border-color) !important;
}

:deep(.ant-dropdown-menu-item) {
  color: var(--text-color-secondary) !important;

  &:hover {
    background-color: var(--hover-bg-color) !important;
  }
}
// 输入框清除图标样式
.ant-input-clear-icon {
  color: var(--text-color-tertiary) !important;
  &:hover {
    color: var(--text-color-tertiary) !important;
  }
}

.ant-select-dropdown {
  background-color: var(--bg-color) !important;
  color: var(--text-color) !important;
  &:hover {
    background-color: var(--hover-bg-color);
  }
}
.ant-select-dropdown .ant-select-item-option {
  font-size: 14px;
  color: var(--text-color) !important;
  background-color: var(--bg-color) !important;
  border: none !important;
}

.ant-select-dropdown .ant-select-item-option:hover {
  background-color: var(--hover-bg-color) !important;
}

.ant-select-dropdown .ant-select-item-option-selected {
  background-color: var(--bg-color-secondary) !important;
  color: var(--text-color) !important;
}

.ant-select-dropdown .ant-select-item-option-active {
  background-color: var(--hover-bg-color) !important;
}

/* 更高优先级的悬浮样式 */
.ant-select-dropdown .ant-select-item-option.ant-select-item-option-active {
  background-color: var(--hover-bg-color) !important;
}

.ant-select-dropdown .ant-select-item-option:hover.ant-select-item-option-active {
  background-color: var(--hover-bg-color) !important;
}

/* 亮色主题专用悬浮样式 */
.theme-light .ant-select-dropdown .ant-select-item-option:hover {
  background-color: rgba(0, 0, 0, 0.12) !important;
}

.theme-light .ant-select-dropdown .ant-select-item-option.ant-select-item-option-active {
  background-color: rgba(0, 0, 0, 0.12) !important;
}

.theme-light .ant-select-dropdown .ant-select-item-option:hover.ant-select-item-option-active {
  background-color: rgba(0, 0, 0, 0.12) !important;
}
.ant-input-clear-icon {
  color: var(--text-color-tertiary);
  &:hover {
    color: var(--text-color-tertiary);
  }
}
.ant-select-clear {
  background: var(--bg-color) !important;
  color: var(--text-color) !important;
}

/*右键菜单样式*/
.v-contextmenu {
  background-color: var(--bg-color) !important;
  padding: 0 4px;
  border: 1px solid var(--text-color-tertiary) !important;
  border-radius: 3px;
  box-shadow: none !important;
}

.v-contextmenu .v-contextmenu-item {
  height: 24px;
  color: var(--text-color) !important;
}
.v-contextmenu .v-contextmenu-item:hover {
  color: var(--text-color);
  border-radius: 3px;
}
//
.commandDialog {
  .ant-modal-content {
    background-color: var(--bg-color) !important;
    border: 1px solid var(--text-color-quinary);
  }
  .ant-modal-title {
    color: var(--text-color);
  }
  .ant-modal-header {
    background: transparent;
  }
  .ant-modal-close-x {
    color: var(--text-color);
  }
  .ant-input {
    color: var(--text-color);
    background-color: var(--bg-color) !important;
    border-color: var(--bg-color-default) !important;
    padding: 4px 11px;

    &::placeholder {
      color: var(--text-color-tertiary) !important;
    }
    &:hover,
    &:focus {
      border-color: #1890ff;
    }
  }
}

// shortcut modal style
.shortcut-modal {
  .ant-modal-content {
    background-color: var(--bg-color) !important;
  }
  .ant-modal-header {
    background-color: var(--bg-color) !important;
    border-bottom-color: var(--border-color) !important;
  }
  .ant-modal-title {
    color: var(--text-color) !important;
  }
  .ant-modal-close-x {
    color: var(--text-color) !important;
  }
  .recording-instruction {
    p {
      color: var(--text-color-secondary) !important;
    }
    .current-shortcut {
      background-color: var(--bg-color-secondary) !important;
      color: var(--text-color) !important;
      border: 1px solid var(--border-color-light) !important;
    }
  }

  .ant-btn-primary[disabled],
  .ant-btn-primary[disabled]:hover {
    background-color: #1890ff !important;
    color: #fff !important;
    border-color: #1890ff !important;
  }
}

.app-region-no-drag {
  -webkit-app-region: no-drag;
}
// Add more component theme styles as needed
