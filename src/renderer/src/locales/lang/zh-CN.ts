export default {
  common: {
    language: '简体中文',
    workspace: '工作空间',
    files: '文件管理',
    keychain: '秘钥',
    extensions: '扩展',
    monitor: '监控',
    ai: 'AI',
    user: '用户',
    setting: '设置',
    notice: '通知',
    logout: '退出登录',
    login: '登录',
    userInfo: '个人信息',
    userConfig: '设置',
    alias: '<PERSON>as配置',
    assetConfig: '资产管理',
    keyChainConfig: '秘钥管理',
    search: '搜索',
    connect: '连接',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    create: '创建',
    cancel: '取消',
    remove: '删除',
    noData: '暂无数据',
    close: '关闭',
    closeOther: '关闭其他',
    closeAll: '全部关闭',
    copy: '复制',
    paste: '粘贴',
    disconnect: '断开连接',
    reconnect: '重新连接',
    newTerminal: '新终端',
    splitRight: '向右分屏',
    splitDown: '向下分屏',
    clearTerm: '清屏',
    shrotenName: '缩短主机名',
    fontsize: '字体大小',
    largen: '放大',
    smaller: '缩小',
    globalExecOn: '全局执行命令(已开)',
    globalExec: '全局执行命令',
    syncInputOn: '同步输入(已开)',
    syncInput: '同步输入',
    allExecuted: '全部执行',
    pleaseLoginFirst: '请先登录',
    select: '选择',
    reset: '重置',
    confirm: '确认',
    ok: '确定',
    quickCommandOn: '快捷命令(已开)',
    quickCommand: '快捷命令',
    add: '添加',
    all: '全部',
    refresh: '刷新',
    fullscreen: '全屏',
    exitFullscreen: '退出全屏',
    editFile: '编辑文件：',
    newFile: '新建文件：',
    saveSuccess: '保存成功',
    saveFailed: '保存失败',
    saveError: '保存出错',
    saveConfirmTitle: '保存更改',
    saveConfirmContent: '您想将更改保存到 {filePath} 吗？',
    pleaseInputLabel: '请输入标签名称',
    pleaseInputPrivateKey: '请输入私钥'
  },
  term: {
    welcome: '欢迎使用 Chaterm'
  },
  login: {
    enterprise: '企业版',
    personal: '个人版',
    contact: '联系我们',
    welcome: '欢迎使用',
    title: 'Chaterm',
    loginByUser: '账号密码登录',
    loginByEmail: '邮箱验证码登录',
    login: '立即登录',
    loggingIn: '正在登录中...',
    skip: '暂不登录？',
    skipLogin: '跳过',
    applyTag: '还没有账号？ ',
    apply: '立即申请',
    pleaseInputUsername: '请输入账号',
    pleaseInputPassword: '请输入密码',
    pleaseInputEmail: '请输入邮箱',
    pleaseInputCode: '请输入验证码',
    invalidEmail: '请输入有效的邮箱地址',
    usernameTooltip: '请输入您的账号',
    passwordTooltip: '请输入您的密码',
    retryAfter: '{seconds}秒后重试',
    getCode: '获取验证码',
    pleaseInputEmailAndCode: '请输入邮箱和验证码',
    codeSent: '验证码已发送',
    codeSendFailed: '验证码发送失败',
    loginFailed: '登录失败',
    externalLoginFailed: '启动外部登录失败',
    loginProcessFailed: '登录处理失败',
    databaseInitFailed: '数据库初始化失败',
    initializationFailed: '初始化失败，请重试',
    routeNavigationFailed: '跳转失败，请重试',
    operationFailed: '操作失败，请重试',
    recordLoginLogFailed: '记录登录日志失败',
    recordLoginFailedLogFailed: '记录登录失败日志失败',
    guestDatabaseInitFailed: '访客数据库初始化失败',
    externalLoginIPDetectionError: '外部登录IP检测异常',
    loginPageIPDetectionError: '登录页面IP检测异常',
    handleProtocolUrlFailed: '处理协议 URL 失败',
    routeJumpFailed: '路由跳转失败',
    skipLoginHandleFailed: '跳过登录处理失败',
    startExternalLoginFailed: '启动外部登录失败',
    databaseInitializationFailed: '数据库初始化失败',
    loginHandleFailed: '登录处理失败',
    authCallbackDetected: '检测到认证回调 URL',
    linuxPlatformHandleAuth: '在 Linux 平台上处理认证回调'
  },
  workspace: {
    workspace: '工作空间',
    personal: '个人空间',
    searchHost: '搜索主机'
  },
  header: {
    title: '智能堡垒机管理系统'
  },
  user: {
    autoCompleteStatus: '自动补全',
    quickVimStatus: '快捷定制化Vim',
    commonVimStatus: '常规定制化Vim',
    aliasStatus: '全局Alias',
    highlightStatus: '全局高亮',
    fontSize: '字体大小(px)',
    cursorStyle: '光标样式',
    scrollBack: '终端回滚量',
    terminalType: '终端类型',
    install: '安装',
    installing: '安装中',
    notInstall: '未安装',
    uninstall: '卸载',
    uninstalling: '卸载中',
    baseSetting: '基础设置',
    terminalSetting: '终端设置',
    ai: 'AI',
    keychain: '秘钥',
    textEditor: '文本编辑器',
    visualVimEditor: '可视化Vim编辑器',
    fileManagerPlugin: '文件管理器扩展',
    fileManagerPluginDescribe: '安装后通过 "鼠标右键-文件管理器" 打开',
    cursorStyleBlock: '块',
    cursorStyleBar: '竖线',
    cursorStyleUnderline: '下划线',
    mouseEvent: '鼠标事件',
    middleMouseEvent: '鼠标中键事件',
    rightMouseEvent: '鼠标右键事件',
    pasteClipboard: '粘贴剪贴板内容',
    showContextMenu: '打开弹出式菜单',
    none: '无',
    watermark: '水印',
    watermarkDescribe: '在终端上显示水印',
    watermarkOpen: '开启',
    watermarkClose: '关闭',
    language: '语言',
    theme: '主题',
    themeDark: '暗色',
    themeLight: '亮色',
    telemetry: '遥测',
    telemetryEnabled: '开启',
    telemetryDisabled: '关闭',
    telemetryDescription:
      '通过发送匿名使用数据和错误报告帮助改进 Chaterm。我们绝不会发送任何代码、提示内容或个人信息。如需了解详细信息，请查看我们的<a href="https://docs.chaterm.ai/user/privacy" target="_blank" rel="noopener noreferrer">隐私政策</a>。',
    enterprise: '企业用户',
    personal: '个人用户',
    name: '名称',
    email: '邮箱',
    mobile: '手机',
    organization: '组织',
    ip: 'IP地址',
    macAddress: 'Mac地址',
    general: '通用',
    extensions: '扩展',
    about: '关于',
    shortcuts: '快捷键',
    shortcutSettings: '快捷键设置',
    shortcutDescription: '自定义应用程序快捷键',
    shortcutAction: '动作',
    shortcutKey: '快捷键',
    shortcutModify: '修改',
    shortcutReset: '重置',
    shortcutSave: '保存',
    shortcutCancel: '取消',
    shortcutConflict: '快捷键冲突',
    shortcutConflictMessage: '此快捷键已被占用，请选择其他快捷键',
    shortcutInvalid: '无效的快捷键',
    shortcutInvalidMessage: '请输入有效的快捷键组合',
    shortcutSaveSuccess: '快捷键保存成功',
    shortcutSaveFailed: '快捷键保存失败',
    shortcutResetSuccess: '快捷键重置成功',
    shortcutPressKeys: '请按下快捷键组合',
    shortcutRecording: '录制中...',
    shortcutClickToModify: '点击修改',
    model: '模型',
    enableExtendedThinking: '开启扩展思考',
    enableExtendedThinkingDescribe: '提高预算可实现更全面、更细致的推理',
    autoApproval: '开启自动执行',
    autoApprovalDescribe: '允许在不要求确认的情况下运行工具',
    features: '特征',
    enableCheckpoints: '开启检查点',
    enableCheckpointsDescribe: '允许在整个任务中保存工作区的检查点',
    openAIReasoningEffort: 'OpenAI推理工作',
    openAIReasoningEffortLow: '低',
    openAIReasoningEffortMedium: '中',
    openAIReasoningEffortHigh: '高',
    proxySettings: '代理配置',
    enableProxy: '开启代理',
    proxyType: '协议',
    proxyHost: '主机名',
    proxyPort: '端口号',
    enableProxyIdentity: '代理身份认证',
    proxyUsername: '账号',
    proxyPassword: '密码',
    shellIntegrationTimeout: 'Shell集成超时（秒）',
    shellIntegrationTimeoutPh: '请输入超时时间（秒）',
    shellIntegrationTimeoutDescribe: '设置等待shell集成激活的时间',
    terminal: '终端',
    apiConfiguration: 'API 配置',
    apiProvider: 'API 提供商',
    apiProviderDescribe:
      '通过提供上述密钥或使用默认的AWS凭据提供程序（即~/.AWS/rentials或环境变量）进行身份验证。这些凭据仅在本地用于从此客户端发出API请求。',
    awsAccessKey: 'AWS Access Key',
    awsAccessKeyPh: '请输入AWS Access Key',
    awsSecretKey: 'AWS Secret Key',
    awsSecretKeyPh: '请输入AWS Secret Key',
    awsSessionToken: 'AWS Session Token',
    awsSessionTokenPh: '请输入AWS  Session Token',
    awsRegion: 'AWS 区域',
    awsRegionPh: '请选择区域',
    awsEndpointSelected: '使用自定义VPC端点',
    awsBedrockEndpointPh: '输入VPC端点URL（可选）',
    awsUseCrossRegionInference: '使用跨区域推理',
    chatSettings: '模式',
    liteLlmBaseUrl: 'URL地址',
    liteLlmBaseUrlPh: '请输入URL地址',
    liteLlmApiKey: 'API Key',
    liteLlmApiKeyPh: '请输入API Key',
    liteLlmApiKeyDescribe: '密钥存储在本地，仅用于从此客户端发出API请求',
    customInstructions: '自定义指令',
    customInstructionsPh: '这些指令添加到每个请求发送的系统提示的末尾。例如：总是用中文回答',
    deepSeekApiKey: 'DeepSeek API Key',
    deepSeekApiKeyPh: '请输入API Key',
    deepSeekApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    openAiBaseUrl: 'OpenAI URL地址',
    openAiBaseUrlPh: '请输入URL地址',
    openAiApiKey: 'OpenAI API Key',
    openAiApiKeyPh: '请输入API Key',
    openAiApiKeyDescribe: '此密钥存储在本地，仅用于从此客户端发出API请求',
    checkModelConfigFailMessage: '缺少必要的模型配置',
    checkModelConfigFailDescription: '请填写必要的模型配置，设置->模型->添加模型->API 配置',
    checkSuccessMessage: '连接成功',
    checkSuccessDescription: 'API密钥有效',
    checkFailMessage: '连接失败',
    checkFailDescriptionDefault: '未知错误',
    checkFailDescriptionMain: '无法连接到主进程',
    models: '模型',
    modelNames: '模型列表',
    aiPreferences: 'AI 偏好设置',
    addModel: '添加模型',
    addModelExistError: '模型名称已存在',
    addModelSuccess: '新增模型成功',
    billing: '计费概览',
    subscription: '订阅类型',
    expires: '过期时间',
    ratio: '用量占比',
    budgetResetAt: '下次重置时间',
    sshAgentSettings: 'SSH Agent设置',
    noKeyAdd: '暂无已添加秘钥',
    remove: '移除',
    comment: '备注',
    fingerprint: '指纹',
    addSuccess: '添加成功',
    addFailed: '添加失败',
    type: '类型'
  },
  extensions: {
    extensions: '扩展',
    alias: '别名',
    fuzzySearch: '模糊搜索',
    aliasDescription: '全局Alias配置',
    command: '命令',
    action: '操作',
    success: '成功',
    error: '失败',
    errorDescription: '创建失败！',
    errorNetWork: '网络请求异常',
    warning: '提示',
    missingAliasCommand: '缺少别名或命令！',
    deleteSuccess: '删除成功！',
    errorDeletingAlias: '删除失败！',
    aliasAlreadyExists: '别名已存在！',
    addCommand: '添加命令'
  },
  shortcuts: {
    actions: {
      openSettings: '打开设置',
      toggleLeftSidebar: '切换左侧边栏',
      toggleRightSidebar: '切换右侧边栏',
      sendOrToggleAi: '发送到AI / 切换AI侧边栏',
      switchToNextTab: '切换到下一个标签页',
      switchToPrevTab: '切换到上一个标签页',
      switchToSpecificTab: '切换到指定标签页[1...9]'
    }
  },
  personal: {
    host: '主机',
    newHost: '添加主机',
    keyChain: '密钥',
    address: '连接信息',
    general: '概述',
    group: '分组',
    accountPassword: '账号密码',
    key: '密钥',
    username: '用户名',
    password: '密码',
    remoteHost: '连接IP或地址',
    port: '端口',
    verificationMethod: '认证方式',
    alias: '别名',
    pleaseInputRemoteHost: '请输入远程连接地址',
    pleaseInputPort: '请输入端口',
    pleaseInputUsername: '请输入用户名',
    pleaseInputPassword: '请输入密码',
    pleaseSelectKeychain: '请选择密钥',
    pleaseInputAlias: '请输入别名或主机名',
    pleaseSelectGroup: '请选择分组',
    personal: '本地连接',
    enterprise: '企业资源',
    editHost: '编辑主机',
    saveAsset: '保存',
    createAsset: '创建',
    deleteConfirm: '删除确认',
    deleteConfirmContent: '确定要删除资产 "{name}" 吗？',
    deleteSuccess: '删除资产 {name} 成功',
    deleteFailure: '删除失败',
    deleteError: '删除出错: {error}',
    createSuccess: '创建资产成功',
    saveSuccess: '保存成功',
    saveError: '保存出错',
    favoriteUpdateSuccess: '资产 {name} 收藏状态已更新',
    favoriteUpdateFailure: '更新收藏状态失败',
    favoriteUpdateError: '更新收藏状态出错',
    defaultGroup: '主机',
    hostType: 'ssh',
    personalAsset: '个人',
    enterpriseAsset: '企业',
    organizationTip: '仅支持 Jumpserver',
    refreshAssets: '资产',
    refreshingAssets: '正在刷新资产...',
    refreshSuccess: '资产刷新成功',
    refreshError: '资产刷新失败',
    validationRemoteHostRequired: '远程连接地址不能为空',
    validationPortRequired: '端口不能为空',
    validationUsernameRequired: '用户名不能为空',
    validationPasswordRequired: '密码不能为空',
    validationKeychainRequired: '密钥不能为空',
    validationEnterpriseRequiredFields: '企业资产的远程地址、端口、用户名、密码或密钥不能为空'
  },
  ai: {
    welcome: '您好，请问需要在终端做什么操作？',
    loginPrompt: '登录后即可使用 AI 功能，新用户注册免费使用两周',
    searchHost: '输入IP搜索',
    noMatchingHosts: '无匹配主机',
    copy: '复制',
    run: '执行',
    reject: '拒绝',
    cancel: '取消',
    resume: '恢复',
    agentMessage: '到任意主机执行命令查询，排查错误和任务处理等任何事情',
    cmdMessage: '到当前活跃终端执行任务，请先连接目标主机',
    chatMessage: '与AI对话，学习，头脑风暴（无法操作服务器）',
    newChat: '新建对话',
    showChatHistory: '显示历史记录',
    closeAiSidebar: '关闭AI侧边栏',
    addHost: '添加主机',
    processing: '处理中...',
    searchHistoryPH: '请输入',
    loading: '加载中...',
    loadMore: '加载',
    copyToClipboard: '已复制到剪贴板',
    retry: '重试',
    taskCompleted: '任务已完成',
    newTask: '开始新任务',
    codePreview: '代码预览 ({lines}行)'
  },
  keyChain: {
    newKey: '添加密钥',
    keyDrop: '拖拽私钥文件到此处导入',
    editKey: '编辑密钥',
    saveKey: '保存密钥',
    createKey: '创建密钥',
    deleteConfirm: '删除确认',
    deleteConfirmContent: '确定要删除密钥 "{name}" 吗？',
    privateKey: '私钥',
    publicKey: '公钥',
    passphrase: '私钥的密码',
    alias: '别名',
    key: '密钥',
    pleaseInput: '请输入',
    name: '名称',
    type: '类型：',
    deleteSuccess: '删除密钥 {name} 成功',
    deleteFailure: '删除失败',
    deleteError: '删除出错: {error}',
    createSuccess: '创建密钥成功',
    saveSuccess: '保存成功',
    saveError: '保存出错'
  },
  userInfo: {
    enterprise: '企业用户',
    personal: '个人用户',
    name: '名称',
    username: '用户名',
    mobile: '手机',
    email: '邮箱',
    organization: '组织',
    ip: 'IP地址',
    macAddress: 'Mac地址',
    password: '密码',
    pleaseInputName: '请输入名称',
    pleaseInputUsername: '请输入用户名',
    pleaseInputMobile: '请输入手机号',
    pleaseInputNewPassword: '请输入新密码',
    nameRequired: '名称不得为空',
    nameTooLong: '名称长度不能超过20位',
    usernameLengthError: '用户名长度需在6-20位之间',
    usernameFormatError: '用户名只能包含字母、数字和下划线',
    mobileInvalid: '请输入有效的手机号',
    passwordLengthError: '密码长度不能小于6位',
    passwordStrengthError: '请具有弱以上的密码强度',
    passwordStrength: '密码强度',
    passwordStrengthWeak: '弱',
    passwordStrengthMedium: '中',
    passwordStrengthStrong: '强',
    updateSuccess: '更新成功',
    updateFailed: '更新失败',
    passwordResetSuccess: '密码重置成功',
    passwordResetFailed: '密码重置失败',
    edit: '编辑',
    save: '保存',
    cancel: '取消',
    resetPassword: '重置密码',
    expirationTime: '过期时间'
  },
  update: {
    available: '有最新版本可以更新',
    update: '更新',
    later: '稍后',
    downloading: '正在下载更新',
    complete: '已下载完毕，是否立即安装',
    install: '安装'
  },
  files: {
    name: '名称',
    permissions: '权限',
    size: '大小',
    modifyDate: '修改日期',
    uploadDirectory: '上传文件夹',
    uploadFile: '上传文件',
    rollback: '返回',
    moveTo: '移动到',
    cpTo: '复制到',
    originPath: '原路径',
    targetPath: '目标路径',
    pathInputTips: '请输入目标路径，如 /root/tmp',
    noDirTips: '暂无子目录',
    dirEdit: '编辑',
    conflictTips: '目标目录已存在同名文件',
    newFileName: '新文件名',
    rename: '重命名',
    overwrite: '覆盖',
    overwriteTips: '是否覆盖或以新名称保存',
    file: '文件',
    exists: '已存在于',
    deleteFileTips: '确认要删除以下文件吗？',
    deleting: '删除中...',
    deleteSuccess: '删除成功',
    deleteFailed: '删除失败',
    deleteError: '删除异常',
    modifySuccess: '修改成功',
    modifyFailed: '修改失败',
    modifyError: '修改异常',
    downloading: '下载中...',
    downloadSuccess: '下载成功',
    downloadFailed: '下载失败',
    downloadError: '下载异常',
    uploading: '上传中...',
    uploadSuccess: '上传成功',
    uploadFailed: '上传失败',
    uploadError: '上传异常',
    copyFileSuccess: '复制文件成功',
    copyFileFailed: '复制文件失败',
    copyFileError: '复制文件失败',
    moveFileSuccess: '移动文件成功',
    moveFileFailed: '移动文件失败',
    moveFileError: '移动文件异常',
    modifyFilePermissionsSuccess: '修改文件权限成功',
    modifyFilePermissionsFailed: '修改文件权限失败',
    modifyFilePermissionsError: '修改文件权限异常',
    read: '读取',
    write: '写入',
    exec: '执行',
    applyToSubdirectories: '应用到子目录',
    publicGroup: '公共组',
    userGroups: '用户组',
    owner: '所有者',
    permissionSettings: '权限设置',
    delete: '删除',
    move: '移动',
    copy: '复制',
    more: '更多',
    download: '下载',
    doubleClickToOpen: '双击打开'
  },
  about: {
    version: '版本',
    checkUpdate: '检查更新',
    latestVersion: '最新版本',
    downLoadUpdate: '下载更新',
    downloading: '正在下载',
    checkUpdateError: '检查更新失败',
    checking: '检查中',
    install: '安装'
  },
  mfa: {
    title: '二次验证',
    verificationCode: '验证码',
    verificationCodeError: '验证码错误',
    pleaseInputVerificationCode: '请输入验证码',
    remainingTime: '剩余时间',
    confirm: '确认',
    cancel: '取消',
    setupGlobalListeners: '设置全局 MFA 监听器'
  },
  logs: {
    logs: '日志',
    loginLogs: '登录日志',
    operationLogs: '操作日志',
    loginLogsDesc: '查看用户登录记录',
    operationLogsDesc: '查看命令操作记录',
    username: '用户名',
    email: '邮箱',
    ipAddress: 'IP地址',
    macAddress: 'MAC地址',
    loginMethod: '登录方式',
    status: '状态',
    platform: '平台',
    loginTime: '登录时间',
    loginSuccess: '登录成功',
    loginFailed: '登录失败',
    unknown: '未知',
    searchByEmail: '按邮箱搜索',
    startDate: '开始日期',
    endDate: '结束日期',
    items: '条',
    fetchError: '获取数据失败',
    reset: '重置',
    searchHostIP: '搜索主机IP',
    searchInputCommand: '搜索输入命令',
    searchUsername: '搜索用户名',
    startTime: '开始时间',
    endTime: '结束时间',
    search: '搜索',
    id: 'ID',
    ip: 'IP',
    commandInput: '命令输入',
    commandOutput: '命令输出',
    operationTime: '操作时间',
    success: '成功',
    failed: '失败',
    timeout: '超时',
    total: '总计',
    records: '条记录',
    getOperationLogsFailed: '获取操作日志失败',
    noData: '暂无数据'
  }
}
