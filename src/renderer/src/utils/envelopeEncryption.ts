/**
 * 🔐 信封加密客户端接口
 *
 * 提供给渲染进程使用的加密/解密接口
 * 通过 IPC 与主进程中的加密服务通信
 */

export interface EncryptionResult {
  encrypted: string
  algorithm: string
  keyFingerprint: string
  userId: string
  iv?: string
  tag?: string
}

export interface EncryptionServiceStatus {
  initialized: boolean
  userId: string | null
  keyFingerprint: string | null
  serverUrl: string
  authStatus: any
}

export interface ServiceResponse {
  success: boolean
  message: string
}

export class EnvelopeEncryptionClient {
  private api: any

  constructor() {
    this.api = (window as any).api
    if (!this.api) {
      throw new Error('Electron API not available')
    }
  }

  /**
   * 加密数据
   * @param plaintext 要加密的明文数据
   * @returns 加密结果
   */
  async encrypt(plaintext: string): Promise<EncryptionResult> {
    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('无效的明文数据')
    }

    try {
      console.log('🔐 请求加密数据...')
      const result = await this.api.invoke('envelope-encryption:encrypt', plaintext)
      console.log('✅ 数据加密完成')
      return result
    } catch (error) {
      console.error('❌ 数据加密失败:', error)
      throw new Error(`加密失败: ${(error as Error).message}`)
    }
  }

  /**
   * 解密数据
   * @param encryptedData 加密的数据对象
   * @returns 解密后的明文
   */
  async decrypt(encryptedData: any): Promise<string> {
    if (!encryptedData || typeof encryptedData !== 'object') {
      throw new Error('无效的加密数据')
    }

    try {
      console.log('🔓 请求解密数据...')
      const result = await this.api.invoke('envelope-encryption:decrypt', encryptedData)
      console.log('✅ 数据解密完成')
      return result
    } catch (error) {
      console.error('❌ 数据解密失败:', error)
      throw new Error(`解密失败: ${(error as Error).message}`)
    }
  }

  /**
   * 初始化加密服务
   * @param userId 用户ID（可选）
   * @returns 初始化结果
   */
  async initialize(userId?: string): Promise<ServiceResponse> {
    try {
      console.log('🔑 初始化加密服务...')
      const result = await this.api.invoke('envelope-encryption:initialize', userId)
      console.log('✅ 加密服务初始化完成:', result.message)
      return result
    } catch (error) {
      console.error('❌ 加密服务初始化失败:', error)
      throw new Error(`初始化失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取服务状态
   * @returns 服务状态信息
   */
  async getStatus(): Promise<EncryptionServiceStatus> {
    try {
      const result = await this.api.invoke('envelope-encryption:status')
      return result
    } catch (error) {
      console.error('❌ 获取服务状态失败:', error)
      throw new Error(`获取状态失败: ${(error as Error).message}`)
    }
  }

  /**
   * 轮换数据密钥
   * @returns 轮换结果
   */
  async rotateDataKey(): Promise<ServiceResponse> {
    try {
      console.log('🔄 轮换数据密钥...')
      const result = await this.api.invoke('envelope-encryption:rotate-key')
      console.log('✅ 密钥轮换完成:', result.message)
      return result
    } catch (error) {
      console.error('❌ 密钥轮换失败:', error)
      throw new Error(`轮换失败: ${(error as Error).message}`)
    }
  }

  /**
   * 健康检查
   * @returns 健康状态
   */
  async healthCheck(): Promise<any> {
    try {
      const result = await this.api.invoke('envelope-encryption:health-check')
      return result
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      throw new Error(`健康检查失败: ${(error as Error).message}`)
    }
  }

  /**
   * 清理服务
   * @param clearStorage 是否清理存储
   * @returns 清理结果
   */
  async cleanup(clearStorage: boolean = false): Promise<ServiceResponse> {
    try {
      console.log('🗑️ 清理加密服务...')
      const result = await this.api.invoke('envelope-encryption:cleanup', clearStorage)
      console.log('✅ 服务清理完成:', result.message)
      return result
    } catch (error) {
      console.error('❌ 服务清理失败:', error)
      throw new Error(`清理失败: ${(error as Error).message}`)
    }
  }

  /**
   * 检查服务是否可用
   * @returns 服务是否可用
   */
  async isAvailable(): Promise<boolean> {
    try {
      const status = await this.getStatus()
      return status.initialized
    } catch (error) {
      console.error('❌ 检查服务可用性失败:', error)
      return false
    }
  }

  /**
   * 批量加密数据
   * @param dataList 要加密的数据列表
   * @returns 加密结果列表
   */
  async encryptBatch(dataList: string[]): Promise<EncryptionResult[]> {
    const results: EncryptionResult[] = []

    for (const data of dataList) {
      try {
        const result = await this.encrypt(data)
        results.push(result)
      } catch (error) {
        console.error(`❌ 批量加密失败 (数据: ${data.substring(0, 20)}...):`, error)
        throw error
      }
    }

    return results
  }

  /**
   * 批量解密数据
   * @param encryptedDataList 要解密的数据列表
   * @returns 解密结果列表
   */
  async decryptBatch(encryptedDataList: any[]): Promise<string[]> {
    const results: string[] = []

    for (const encryptedData of encryptedDataList) {
      try {
        const result = await this.decrypt(encryptedData)
        results.push(result)
      } catch (error) {
        console.error('❌ 批量解密失败:', error)
        throw error
      }
    }

    return results
  }
}

// 创建单例实例
export const envelopeEncryptionClient = new EnvelopeEncryptionClient()

// 便捷函数
export const encryptData = (plaintext: string) => envelopeEncryptionClient.encrypt(plaintext)
export const decryptData = (encryptedData: any) => envelopeEncryptionClient.decrypt(encryptedData)
export const initializeEncryption = (userId?: string) => envelopeEncryptionClient.initialize(userId)
export const getEncryptionStatus = () => envelopeEncryptionClient.getStatus()
export const rotateEncryptionKey = () => envelopeEncryptionClient.rotateDataKey()
export const checkEncryptionHealth = () => envelopeEncryptionClient.healthCheck()
export const cleanupEncryption = (clearStorage?: boolean) => envelopeEncryptionClient.cleanup(clearStorage)
export const isEncryptionAvailable = () => envelopeEncryptionClient.isAvailable()

// 默认导出
export default envelopeEncryptionClient
