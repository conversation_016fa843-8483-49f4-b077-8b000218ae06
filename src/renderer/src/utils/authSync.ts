/**
 * 🔗 认证信息同步工具
 * 
 * 用于将渲染进程的认证信息同步到主进程
 * 确保主进程的加密服务能够获取到最新的认证信息
 */

import { getUserInfo } from './permission'

export class AuthSyncManager {
  private api: any
  private syncInterval: NodeJS.Timeout | null = null
  private lastSyncedToken: string | null = null

  constructor() {
    this.api = (window as any).api
    if (!this.api) {
      console.warn('⚠️ Electron API not available, auth sync disabled')
    }
  }

  /**
   * 同步认证信息到主进程
   */
  async syncAuthToMain(): Promise<void> {
    if (!this.api) {
      return
    }

    try {
      // 获取当前用户信息
      const userInfo = getUserInfo()
      const ctmToken = localStorage.getItem('ctm-token')
      
      if (!userInfo || !ctmToken) {
        console.log('🔄 没有认证信息需要同步')
        return
      }

      // 检查是否需要同步（token 是否有变化）
      if (this.lastSyncedToken === ctmToken) {
        return
      }

      // 准备认证数据
      const authData = {
        token: ctmToken,
        userId: userInfo.uid?.toString() || userInfo.id?.toString(),
        expiry: Date.now() + 24 * 60 * 60 * 1000 // 默认24小时过期
      }

      // 同步到主进程
      await this.api.invoke('auth:update-token', authData)
      this.lastSyncedToken = ctmToken
      
      console.log('🔑 认证信息已同步到主进程')
    } catch (error) {
      console.error('❌ 同步认证信息失败:', error)
    }
  }

  /**
   * 清除主进程中的认证信息
   */
  async clearAuthFromMain(): Promise<void> {
    if (!this.api) {
      return
    }

    try {
      await this.api.invoke('auth:clear-token')
      this.lastSyncedToken = null
      console.log('🗑️ 已清除主进程中的认证信息')
    } catch (error) {
      console.error('❌ 清除主进程认证信息失败:', error)
    }
  }

  /**
   * 开始自动同步
   * @param intervalMs 同步间隔（毫秒），默认30秒
   */
  startAutoSync(intervalMs: number = 30000): void {
    if (this.syncInterval) {
      this.stopAutoSync()
    }

    // 立即同步一次
    this.syncAuthToMain()

    // 设置定时同步
    this.syncInterval = setInterval(() => {
      this.syncAuthToMain()
    }, intervalMs)

    console.log(`🔄 已启动认证信息自动同步 (间隔: ${intervalMs}ms)`)
  }

  /**
   * 停止自动同步
   */
  stopAutoSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval)
      this.syncInterval = null
      console.log('⏹️ 已停止认证信息自动同步')
    }
  }

  /**
   * 监听存储变化，自动同步
   */
  watchStorageChanges(): void {
    // 监听 localStorage 变化
    window.addEventListener('storage', (event) => {
      if (event.key === 'ctm-token' || event.key === 'userInfo') {
        console.log('🔄 检测到认证信息变化，触发同步')
        this.syncAuthToMain()
      }
    })

    // 监听用户信息变化（通过 Pinia store）
    try {
      const { userInfoStore } = require('@/store/index')
      const { pinia } = require('@/main')
      
      const userStore = userInfoStore(pinia)
      
      // 监听 store 变化
      userStore.$subscribe((mutation: any, state: any) => {
        if (mutation.type === 'direct' && state.userInfo?.token) {
          console.log('🔄 检测到用户信息变化，触发同步')
          this.syncAuthToMain()
        }
      })
    } catch (error) {
      console.warn('⚠️ 无法监听用户信息变化:', error)
    }
  }

  /**
   * 手动触发同步
   */
  async forcSync(): Promise<void> {
    this.lastSyncedToken = null // 强制同步
    await this.syncAuthToMain()
  }
}

// 创建单例实例
export const authSyncManager = new AuthSyncManager()

// 便捷函数
export const syncAuthToMain = () => authSyncManager.syncAuthToMain()
export const clearAuthFromMain = () => authSyncManager.clearAuthFromMain()
export const startAuthAutoSync = (intervalMs?: number) => authSyncManager.startAutoSync(intervalMs)
export const stopAuthAutoSync = () => authSyncManager.stopAutoSync()
export const watchAuthChanges = () => authSyncManager.watchStorageChanges()
export const forceAuthSync = () => authSyncManager.forcSync()

// 默认导出
export default authSyncManager
