/**
 * 🔐 信封加密服务 - Chaterm 项目适配版
 *
 * 提供统一的加密/解密接口，在主进程中注册使用
 * 桥接原有的 envelope_encryption JS 代码和 Chaterm 的 TS 项目
 */

import { ipcMain } from 'electron'

// 导入原有的 JS 模块（保持不变）
const ClientSideCrypto = require('./clientSideCrypto')
const { chatermAuthAdapter } = require('./adapters/chatermAuthAdapter')
const config = require('./config')

export interface EncryptionResult {
  encrypted: string
  algorithm: string
  keyFingerprint: string
  userId: string
  iv?: string
  tag?: string
}

export interface EncryptionServiceStatus {
  initialized: boolean
  userId: string | null
  keyFingerprint: string | null
  serverUrl: string
  authStatus: any
}

export class EnvelopeEncryptionService {
  private clientCrypto: any = null
  private isInitialized: boolean = false
  private currentUserId: string | null = null
  private initializationFailed: boolean = false
  private lastInitError: string | null = null
  private retryCount: number = 0
  private maxRetries: number = 3

  constructor(serverUrl?: string) {
    // 使用默认的 KMS 服务器地址，或从配置中获取
    const kmsServerUrl = serverUrl || config.serverUrl
    this.clientCrypto = new ClientSideCrypto(kmsServerUrl)
    this.setupIPCHandlers()
  }

  /**
   * 设置 IPC 处理器
   */
  private setupIPCHandlers(): void {
    // 加密数据
    ipcMain.handle('envelope-encryption:encrypt', async (event, plaintext: string) => {
      try {
        return await this.encrypt(plaintext)
      } catch (error) {
        console.error('❌ IPC 加密失败:', error)
        throw error
      }
    })

    // 解密数据
    ipcMain.handle('envelope-encryption:decrypt', async (event, encryptedData: any) => {
      try {
        return await this.decrypt(encryptedData)
      } catch (error) {
        console.error('❌ IPC 解密失败:', error)
        throw error
      }
    })

    // 初始化服务
    ipcMain.handle('envelope-encryption:initialize', async (event, userId?: string) => {
      try {
        return await this.initialize(userId)
      } catch (error) {
        console.error('❌ IPC 初始化失败:', error)
        throw error
      }
    })

    // 获取服务状态
    ipcMain.handle('envelope-encryption:status', async () => {
      try {
        return this.getStatus()
      } catch (error) {
        console.error('❌ IPC 获取状态失败:', error)
        throw error
      }
    })

    // 轮换密钥
    ipcMain.handle('envelope-encryption:rotate-key', async () => {
      try {
        return await this.rotateDataKey()
      } catch (error) {
        console.error('❌ IPC 轮换密钥失败:', error)
        throw error
      }
    })

    // 健康检查
    ipcMain.handle('envelope-encryption:health-check', async () => {
      try {
        return await this.healthCheck()
      } catch (error) {
        console.error('❌ IPC 健康检查失败:', error)
        throw error
      }
    })

    // 清理服务
    ipcMain.handle('envelope-encryption:cleanup', async (event, clearStorage: boolean = false) => {
      try {
        return this.cleanup(clearStorage)
      } catch (error) {
        console.error('❌ IPC 清理失败:', error)
        throw error
      }
    })
  }

  /**
   * 初始化加密服务（支持网络容错）
   * @param userId 用户ID，如果不提供则从认证适配器获取
   * @param silent 是否静默初始化（不抛出错误）
   */
  async initialize(userId?: string, silent: boolean = false): Promise<{ success: boolean; message: string }> {
    let targetUserId: string | null = null

    try {
      // 获取用户ID
      targetUserId = userId || (await chatermAuthAdapter.getCurrentUserId())
      if (!targetUserId) {
        throw new Error('无法获取用户ID')
      }

      // 获取认证令牌
      const authToken = await chatermAuthAdapter.getAuthToken()
      if (!authToken) {
        console.warn('⚠️ 未获取到认证令牌，将尝试无令牌初始化')
      }

      // 强制密钥轮换：每次启动时清理旧的密钥数据
      console.log('🔄 强制密钥轮换，清理旧的密钥数据...')
      await this.clearStoredKeys(targetUserId)

      // 初始化客户端加密（可能因网络问题失败）
      await this.clientCrypto.initialize(targetUserId, authToken)

      // 初始化成功，重置错误状态
      this.isInitialized = true
      this.currentUserId = targetUserId
      this.initializationFailed = false
      this.lastInitError = null
      this.retryCount = 0

      console.log('✅ 信封加密服务初始化成功')
      console.log('🔑 已获取新的数据密钥')
      return { success: true, message: '加密服务初始化成功' }
    } catch (error) {
      const errorMessage = (error as Error).message

      // 检查是否为网络相关错误
      const isNetworkError = this.isNetworkError(error as Error)

      if (isNetworkError) {
        console.warn('🌐 网络连接问题，信封加密服务初始化失败')
        console.warn(`   错误详情: ${errorMessage}`)
        console.warn('   将在下次加密请求时重试初始化')

        // 标记初始化失败但不影响主进程
        this.initializationFailed = true
        this.lastInitError = errorMessage
        this.isInitialized = false
        this.currentUserId = targetUserId // 保存用户ID用于重试

        if (!silent) {
          return { success: false, message: `网络连接失败，加密服务暂时不可用: ${errorMessage}` }
        }
      } else {
        console.error('❌ 信封加密服务初始化失败:', error)
        this.isInitialized = false
        this.currentUserId = null
        this.initializationFailed = true
        this.lastInitError = errorMessage
      }

      return { success: false, message: `初始化失败: ${errorMessage}` }
    }
  }

  /**
   * 加密数据（支持延迟初始化）
   * @param plaintext 要加密的明文数据
   * @returns 加密结果
   */
  async encrypt(plaintext: string): Promise<EncryptionResult> {
    // 检查数据有效性
    if (!plaintext || typeof plaintext !== 'string') {
      throw new Error('无效的明文数据')
    }

    // 如果服务未初始化，尝试延迟初始化
    if (!this.isInitialized) {
      if (this.initializationFailed) {
        console.log('🔄 检测到网络可能已恢复，尝试重新初始化加密服务...')
        const reinitSuccess = await this.tryReinitialize()

        if (!reinitSuccess) {
          throw new Error(`加密服务不可用: ${this.lastInitError || '初始化失败'}`)
        }
      } else {
        throw new Error('加密服务未初始化，请先调用 initialize()')
      }
    }

    try {
      console.log('🔐 开始加密数据...')
      const result = await this.clientCrypto.encryptData(plaintext)
      console.log('✅ 数据加密完成')
      return result as EncryptionResult
    } catch (error) {
      // 检查是否为网络错误，如果是则标记需要重新初始化
      if (this.isNetworkError(error as Error)) {
        console.warn('🌐 加密过程中检测到网络错误，标记服务需要重新初始化')
        this.isInitialized = false
        this.initializationFailed = true
        this.lastInitError = (error as Error).message
      }

      console.error('❌ 数据加密失败:', error)
      throw new Error(`加密失败: ${(error as Error).message}`)
    }
  }

  /**
   * 解密数据（支持延迟初始化）
   * @param encryptedData 加密的数据对象
   * @returns 解密后的明文
   */
  async decrypt(encryptedData: any): Promise<string> {
    // 检查数据有效性
    if (!encryptedData || typeof encryptedData !== 'object') {
      throw new Error('无效的加密数据')
    }

    // 如果服务未初始化，尝试延迟初始化
    if (!this.isInitialized) {
      if (this.initializationFailed) {
        console.log('🔄 检测到网络可能已恢复，尝试重新初始化加密服务...')
        const reinitSuccess = await this.tryReinitialize()

        if (!reinitSuccess) {
          throw new Error(`加密服务不可用: ${this.lastInitError || '初始化失败'}`)
        }
      } else {
        throw new Error('加密服务未初始化，请先调用 initialize()')
      }
    }

    try {
      console.log('🔓 开始解密数据...')
      const result = await this.clientCrypto.decryptData(encryptedData)
      console.log('✅ 数据解密完成')
      return result
    } catch (error) {
      // 检查是否为网络错误，如果是则标记需要重新初始化
      if (this.isNetworkError(error as Error)) {
        console.warn('🌐 解密过程中检测到网络错误，标记服务需要重新初始化')
        this.isInitialized = false
        this.initializationFailed = true
        this.lastInitError = (error as Error).message
      }

      console.error('❌ 数据解密失败:', error)
      throw new Error(`解密失败: ${(error as Error).message}`)
    }
  }

  /**
   * 轮换数据密钥
   */
  async rotateDataKey(): Promise<{ success: boolean; message: string }> {
    if (!this.isInitialized) {
      throw new Error('加密服务未初始化')
    }

    try {
      await this.clientCrypto.rotateDataKey()
      return { success: true, message: '密钥轮换成功' }
    } catch (error) {
      console.error('❌ 密钥轮换失败:', error)
      return { success: false, message: `密钥轮换失败: ${(error as Error).message}` }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    try {
      const health = await this.clientCrypto.healthCheck()
      const authStatus = chatermAuthAdapter.getAuthStatus()

      return {
        ...health,
        authAdapter: authStatus,
        service: {
          initialized: this.isInitialized,
          userId: this.currentUserId
        }
      }
    } catch (error) {
      console.error('❌ 健康检查失败:', error)
      return {
        service: {
          status: 'error',
          error: (error as Error).message,
          initialized: this.isInitialized,
          userId: this.currentUserId
        }
      }
    }
  }

  /**
   * 获取服务状态
   */
  getStatus(): EncryptionServiceStatus {
    const clientStatus = this.clientCrypto?.getStatus() || {}
    const authStatus = chatermAuthAdapter.getAuthStatus()

    return {
      initialized: this.isInitialized,
      userId: this.currentUserId,
      keyFingerprint: clientStatus.keyFingerprint || null,
      serverUrl: clientStatus.serverUrl || 'unknown',
      authStatus
    }
  }

  /**
   * 清理服务
   * @param clearStorage 是否清理存储
   */
  cleanup(clearStorage: boolean = false): { success: boolean; message: string } {
    try {
      if (this.clientCrypto) {
        this.clientCrypto.cleanup(clearStorage)
      }

      this.isInitialized = false
      this.currentUserId = null

      if (clearStorage) {
        chatermAuthAdapter.clearAuthInfo()
      }

      console.log('✅ 加密服务清理完成')
      return { success: true, message: '服务清理完成' }
    } catch (error) {
      console.error('❌ 服务清理失败:', error)
      return { success: false, message: `清理失败: ${(error as Error).message}` }
    }
  }

  /**
   * 检查是否为网络相关错误
   * @param error 错误对象
   * @returns 是否为网络错误
   */
  private isNetworkError(error: Error): boolean {
    const errorMessage = error.message.toLowerCase()
    const networkErrorKeywords = [
      'network',
      'timeout',
      'connection',
      'econnrefused',
      'enotfound',
      'econnreset',
      'etimedout',
      'fetch failed',
      'request failed',
      'unable to connect',
      'connection refused',
      'dns lookup failed',
      'socket hang up'
    ]

    return networkErrorKeywords.some((keyword) => errorMessage.includes(keyword))
  }

  /**
   * 尝试重新初始化（用于延迟初始化）
   * @returns 初始化结果
   */
  private async tryReinitialize(): Promise<boolean> {
    if (!this.currentUserId) {
      console.warn('⚠️ 无法重新初始化：缺少用户ID')
      return false
    }

    if (this.retryCount >= this.maxRetries) {
      console.warn(`⚠️ 已达到最大重试次数 (${this.maxRetries})，停止重试`)
      return false
    }

    this.retryCount++
    console.log(`🔄 尝试重新初始化加密服务 (第 ${this.retryCount} 次)...`)

    const result = await this.initialize(this.currentUserId, true)
    return result.success
  }

  /**
   * 清理存储的密钥数据
   * @param userId 用户ID
   */
  private async clearStoredKeys(userId: string): Promise<void> {
    try {
      // 导入存储管理器
      const { StorageManager } = require('./utils/storage')
      const storage = new StorageManager()

      // 清理所有存储数据（包括加密的数据密钥和会话信息）
      await storage.cleanup(userId)

      console.log(`🗑️ 已清理用户 ${userId} 的存储密钥数据`)
    } catch (error) {
      console.warn('⚠️ 清理存储密钥时出错:', error)
      // 不抛出错误，允许继续初始化
    }
  }

  /**
   * 设置认证信息（用于初始化时）
   */
  setAuthInfo(token: string, userId: string, expiry?: number): void {
    chatermAuthAdapter.setAuthInfo(token, userId, expiry)
  }
}

// 导出单例实例 - 使用配置文件中的设置
export const envelopeEncryptionService = new EnvelopeEncryptionService()
