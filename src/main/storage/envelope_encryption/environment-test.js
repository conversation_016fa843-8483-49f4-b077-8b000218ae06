/**
 * 🖥️ Electron 主进程环境测试
 * 
 * 验证在 Electron 主进程中的配置和存储是否正确
 */

const config = require('./config')
const { StorageManager } = require('./utils/storage')
const path = require('path')
const fs = require('fs').promises

/**
 * 测试环境检测
 */
async function testEnvironmentDetection() {
  console.log('\n🖥️ === Electron 主进程环境检测 ===')
  
  try {
    // 1. 检查环境变量
    console.log('1. 检查运行环境...')
    console.log(`   - Node.js 版本: ${process.version}`)
    console.log(`   - 平台: ${process.platform}`)
    console.log(`   - 架构: ${process.arch}`)
    console.log(`   - 工作目录: ${process.cwd()}`)
    
    // 2. 检查是否在浏览器环境
    console.log('\n2. 检查浏览器环境...')
    const hasBrowserEnv = typeof window !== 'undefined'
    console.log(`   - 浏览器环境: ${hasBrowserEnv ? '是' : '否'}`)
    
    if (!hasBrowserEnv) {
      console.log('   ✅ 确认运行在 Node.js 环境（Electron 主进程）')
    } else {
      console.log('   ⚠️ 检测到浏览器环境，这不应该发生在主进程中')
    }
    
    // 3. 检查 Electron 相关模块
    console.log('\n3. 检查 Electron 模块...')
    try {
      const { app } = require('electron')
      console.log('   ✅ 成功导入 Electron app 模块')
      console.log(`   - 应用名称: ${app.getName()}`)
      console.log(`   - 应用版本: ${app.getVersion()}`)
    } catch (error) {
      console.log('   ⚠️ 无法导入 Electron 模块:', error.message)
    }
    
    return { success: true, isNodeEnv: !hasBrowserEnv }
  } catch (error) {
    console.error('❌ 环境检测失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试存储配置
 */
async function testStorageConfiguration() {
  console.log('\n💾 === 存储配置测试 ===')
  
  try {
    // 1. 检查配置
    console.log('1. 检查存储配置...')
    console.log(`   - 存储提供者: ${config.storage.provider}`)
    console.log(`   - 密钥前缀: ${config.storage.keyPrefix}`)
    console.log(`   - 会话前缀: ${config.storage.sessionPrefix}`)
    
    // 2. 验证存储提供者
    console.log('\n2. 验证存储提供者...')
    const storage = new StorageManager()
    console.log('   ✅ 存储管理器创建成功')
    
    // 3. 检查存储目录
    console.log('\n3. 检查存储目录...')
    const storageDir = path.join(__dirname, '..', 'temp-storage')
    console.log(`   - 存储目录: ${storageDir}`)
    
    try {
      await fs.access(storageDir)
      console.log('   ✅ 存储目录已存在')
      
      // 列出目录内容
      const files = await fs.readdir(storageDir)
      console.log(`   - 目录文件数: ${files.length}`)
      if (files.length > 0) {
        console.log(`   - 文件列表: ${files.join(', ')}`)
      }
    } catch (error) {
      console.log('   ⚠️ 存储目录不存在，将在首次使用时创建')
    }
    
    return { success: true, storageProvider: config.storage.provider }
  } catch (error) {
    console.error('❌ 存储配置测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试文件存储功能
 */
async function testFileStorage() {
  console.log('\n📁 === 文件存储功能测试 ===')
  
  try {
    const storage = new StorageManager()
    const testKey = 'test-key-' + Date.now()
    const testValue = { message: 'Hello from Electron main process!', timestamp: Date.now() }
    
    // 1. 测试存储
    console.log('1. 测试数据存储...')
    await storage.provider.setItem(testKey, JSON.stringify(testValue))
    console.log('   ✅ 数据存储成功')
    
    // 2. 测试读取
    console.log('2. 测试数据读取...')
    const retrievedValue = await storage.provider.getItem(testKey)
    const parsedValue = JSON.parse(retrievedValue)
    console.log('   ✅ 数据读取成功')
    console.log(`   - 原始数据: ${JSON.stringify(testValue)}`)
    console.log(`   - 读取数据: ${JSON.stringify(parsedValue)}`)
    
    // 3. 验证数据一致性
    console.log('3. 验证数据一致性...')
    const isConsistent = parsedValue.message === testValue.message && 
                        parsedValue.timestamp === testValue.timestamp
    console.log(`   - 数据一致性: ${isConsistent ? '✅ 通过' : '❌ 失败'}`)
    
    // 4. 测试删除
    console.log('4. 测试数据删除...')
    await storage.provider.removeItem(testKey)
    console.log('   ✅ 数据删除成功')
    
    // 5. 验证删除
    console.log('5. 验证数据已删除...')
    const deletedValue = await storage.provider.getItem(testKey)
    const isDeleted = deletedValue === null
    console.log(`   - 删除验证: ${isDeleted ? '✅ 通过' : '❌ 失败'}`)
    
    return { success: isConsistent && isDeleted }
  } catch (error) {
    console.error('❌ 文件存储测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试路径解析
 */
async function testPathResolution() {
  console.log('\n🗂️ === 路径解析测试 ===')
  
  try {
    // 1. 检查当前文件路径
    console.log('1. 检查当前文件路径...')
    console.log(`   - __filename: ${__filename}`)
    console.log(`   - __dirname: ${__dirname}`)
    
    // 2. 检查存储路径
    console.log('\n2. 检查存储路径...')
    const storageDir = path.join(__dirname, '..', 'temp-storage')
    const absoluteStorageDir = path.resolve(storageDir)
    console.log(`   - 相对路径: ${storageDir}`)
    console.log(`   - 绝对路径: ${absoluteStorageDir}`)
    
    // 3. 检查路径是否在项目内
    console.log('\n3. 检查路径位置...')
    const projectRoot = process.cwd()
    const isInProject = absoluteStorageDir.startsWith(projectRoot)
    console.log(`   - 项目根目录: ${projectRoot}`)
    console.log(`   - 存储在项目内: ${isInProject ? '✅ 是' : '❌ 否'}`)
    
    return { success: true, storageDir: absoluteStorageDir, isInProject }
  } catch (error) {
    console.error('❌ 路径解析测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行所有环境测试
 */
async function runAllEnvironmentTests() {
  console.log('🚀 开始 Electron 主进程环境测试...')
  console.log('=' .repeat(60))
  
  const results = {
    environment: null,
    storage: null,
    fileStorage: null,
    pathResolution: null
  }
  
  try {
    // 运行所有测试
    results.environment = await testEnvironmentDetection()
    results.storage = await testStorageConfiguration()
    results.fileStorage = await testFileStorage()
    results.pathResolution = await testPathResolution()
    
    // 输出测试总结
    console.log('\n📊 === 测试总结 ===')
    console.log(`环境检测: ${results.environment.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`存储配置: ${results.storage.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`文件存储: ${results.fileStorage.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`路径解析: ${results.pathResolution.success ? '✅ 通过' : '❌ 失败'}`)
    
    const allPassed = Object.values(results).every(result => result.success)
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`)
    
    if (allPassed) {
      console.log('\n🎉 Electron 主进程环境配置正确！')
      console.log('   - 存储提供者: 文件存储')
      console.log('   - 数据持久化: 支持')
      console.log('   - 环境适配: 完成')
    }
    
    return { success: allPassed, results }
    
  } catch (error) {
    console.error('❌ 环境测试运行失败:', error)
    return { success: false, error: error.message, results }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllEnvironmentTests().catch(console.error)
}

module.exports = {
  testEnvironmentDetection,
  testStorageConfiguration,
  testFileStorage,
  testPathResolution,
  runAllEnvironmentTests
}
