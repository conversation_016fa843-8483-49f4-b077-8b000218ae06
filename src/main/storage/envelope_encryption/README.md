# 🔐 信封加密服务 - Chaterm 项目适配版

## 概述

这是从 KMS 信封加密项目适配到 Chaterm 项目的客户端加密库。专为 **Electron 主进程环境** 设计，提供了完整的端到端加密解决方案，确保敏感数据在客户端加密，服务端无法看到明文数据。

## 🛡️ 安全架构

### 信封加密原理

1. **主密钥(Master Key)**: 存储在 KMS 服务器，用于加密数据密钥
2. **数据密钥(Data Key)**: 临时生成，用于实际的数据加密/解密
3. **加密的数据密钥**: 由主密钥加密后存储在客户端
4. **数据不离开客户端**: 所有敏感数据在客户端加密，服务端只处理密钥管理

### 安全特性

- 🔐 **数据不离开客户端**: 所有敏感数据在客户端加密
- 🔑 **密钥分离架构**: 主密钥在KMS，数据密钥临时下发
- 🛡️ **零信任安全**: 服务端无法看到用户数据
- 🔄 **密钥轮换支持**: 支持定期更换数据密钥
- 💾 **安全存储**: 只存储加密后的数据密钥
- 🚀 **启动时强制轮换**: 每次程序启动时自动获取新的数据密钥
- 🖥️ **Electron 主进程优化**: 专为 Node.js 环境设计，使用文件存储确保数据持久化
- 🌐 **网络容错机制**: 网络问题不影响主进程启动，支持网络恢复后自动重试

## 📁 项目结构

```
src/main/storage/envelope_encryption/
├── adapters/
│   └── chatermAuthAdapter.js     # Chaterm 认证适配器（新增）
├── services/
│   └── apiClient.js              # API 客户端（原有代码）
├── utils/
│   ├── crypto.js                 # 加密工具（原有代码）
│   ├── storage.js                # 存储管理（已适配 Electron 主进程）
│   └── tempFileStorage.js        # 文件存储（原有代码，主进程持久化）
├── clientSideCrypto.js           # 核心加密类（原有代码）
├── config.js                     # 配置文件（已适配 Node.js 环境）
├── index.js                      # 主入口（原有代码）
├── service.ts                    # TypeScript 服务包装器（新增）
├── example.js                    # 使用示例（新增）
└── README.md                     # 说明文档（新增）

# 存储位置（自动创建）
src/main/storage/temp-storage/    # 加密密钥文件存储目录
```

## 🚀 快速开始

### 1. 在渲染进程中使用

```typescript
import { envelopeEncryptionClient, encryptData, decryptData } from '@/utils/envelopeEncryption'

// 加密数据
const sensitiveData = 'This is sensitive information'
const encryptedResult = await encryptData(sensitiveData)
console.log('加密结果:', encryptedResult)

// 解密数据
const decryptedData = await decryptData(encryptedResult)
console.log('解密结果:', decryptedData)
```

### 2. 检查服务状态

```typescript
import { getEncryptionStatus, isEncryptionAvailable } from '@/utils/envelopeEncryption'

// 检查服务是否可用
const isAvailable = await isEncryptionAvailable()
if (isAvailable) {
  console.log('加密服务可用')
} else {
  console.log('加密服务不可用')
}

// 获取详细状态
const status = await getEncryptionStatus()
console.log('服务状态:', status)
```

### 3. 认证信息同步

```typescript
import { authSyncManager, startAuthAutoSync } from '@/utils/authSync'

// 启动自动同步认证信息到主进程
startAuthAutoSync(30000) // 每30秒同步一次

// 监听认证信息变化
authSyncManager.watchStorageChanges()
```

### 4. 网络容错处理

```typescript
import { encryptData, decryptData, getEncryptionStatus } from '@/utils/envelopeEncryption'

// 网络问题时的优雅处理
try {
  const encrypted = await encryptData('sensitive data')
  console.log('加密成功')
} catch (error) {
  if (error.message.includes('网络连接失败')) {
    console.log('网络暂时不可用，请稍后重试')
    // 应用可以继续运行，加密服务会在网络恢复后自动重试
  }
}

// 检查加密服务状态
const status = await getEncryptionStatus()
console.log('加密服务可用:', status.initialized)
```

## 🔧 配置

### 环境变量

```bash
# KMS 服务器地址
KMS_SERVER_URL=http://localhost:3000

# 开发环境标识
NODE_ENV=development
```

### 配置文件

修改 `config.js` 中的配置：

```javascript
const config = {
  // 🌐 服务端API地址
  serverUrl: process.env.KMS_SERVER_URL || 'http://localhost:3000',

  // 🔐 加密配置
  encryption: {
    algorithm: 'aes-256-gcm',
    keyLength: 32,
    ivLength: 16,
    tagLength: 16
  }

  // 其他配置...
}
```

## 📚 API 参考

### 主进程服务 (service.ts)

```typescript
class EnvelopeEncryptionService {
  // 初始化服务
  async initialize(userId?: string): Promise<ServiceResponse>

  // 加密数据
  async encrypt(plaintext: string): Promise<EncryptionResult>

  // 解密数据
  async decrypt(encryptedData: any): Promise<string>

  // 轮换密钥
  async rotateDataKey(): Promise<ServiceResponse>

  // 健康检查
  async healthCheck(): Promise<any>

  // 获取状态
  getStatus(): EncryptionServiceStatus

  // 清理服务
  cleanup(clearStorage?: boolean): ServiceResponse
}
```

### 渲染进程客户端 (envelopeEncryption.ts)

```typescript
class EnvelopeEncryptionClient {
  // 加密数据
  async encrypt(plaintext: string): Promise<EncryptionResult>

  // 解密数据
  async decrypt(encryptedData: any): Promise<string>

  // 批量加密
  async encryptBatch(dataList: string[]): Promise<EncryptionResult[]>

  // 批量解密
  async decryptBatch(encryptedDataList: any[]): Promise<string[]>

  // 其他方法...
}
```

## 🔄 工作流程

### 1. 初始化流程

```mermaid
sequenceDiagram
    participant R as 渲染进程
    participant M as 主进程
    participant K as KMS服务器

    R->>M: 用户登录，初始化数据库
    M->>M: 获取用户认证信息
    M->>M: 初始化加密服务
    M->>K: 请求数据密钥
    K->>M: 返回加密的数据密钥
    M->>M: 存储加密的数据密钥
```

### 2. 加密流程

```mermaid
sequenceDiagram
    participant R as 渲染进程
    participant M as 主进程

    R->>M: 请求加密数据
    M->>M: 使用数据密钥加密
    M->>R: 返回加密结果
```

### 3. 解密流程

```mermaid
sequenceDiagram
    participant R as 渲染进程
    participant M as 主进程

    R->>M: 请求解密数据
    M->>M: 使用数据密钥解密
    M->>R: 返回明文数据
```

## 🧪 测试

运行示例代码：

```bash
# 在主进程中运行示例
node src/main/storage/envelope_encryption/example.js
```

## 🌐 网络容错机制

### 工作原理

1. **静默初始化** - 主进程启动时，加密服务初始化失败不会影响应用启动
2. **网络错误检测** - 自动识别网络相关错误（连接超时、DNS失败等）
3. **延迟初始化** - 首次加密/解密请求时自动重试初始化
4. **智能重试** - 有限次数重试，避免无限循环

### 错误处理流程

```mermaid
flowchart TD
    A[应用启动] --> B[尝试初始化加密服务]
    B --> C{网络连接正常?}
    C -->|是| D[初始化成功]
    C -->|否| E[标记初始化失败]
    E --> F[主进程继续启动]
    F --> G[用户请求加密]
    G --> H{服务已初始化?}
    H -->|是| I[执行加密]
    H -->|否| J[尝试重新初始化]
    J --> K{重试成功?}
    K -->|是| I
    K -->|否| L[返回错误信息]
```

### 支持的网络错误类型

- `ECONNREFUSED` - 连接被拒绝
- `ETIMEDOUT` - 连接超时
- `ENOTFOUND` - DNS 查找失败
- `ECONNRESET` - 连接被重置
- `fetch failed` - 网络请求失败

### 测试网络容错

```bash
# 运行网络容错测试
node src/main/storage/envelope_encryption/network-resilience-test.js
```

## 🔍 故障排除

### 常见问题

1. **网络连接问题**

   - 检查网络连接是否正常
   - 确认 KMS 服务器地址是否正确
   - 查看是否有防火墙或代理阻止连接
   - 网络恢复后，加密服务会自动重试

2. **初始化失败**

   - 检查 KMS 服务器是否可访问
   - 确认用户认证信息是否正确
   - 查看控制台错误日志
   - 如果是网络问题，主进程会继续运行

3. **加密/解密失败**

   - 确认服务已正确初始化
   - 检查数据密钥是否有效
   - 验证加密数据格式是否正确
   - 网络错误时会自动尝试重新初始化

4. **认证问题**
   - 确认 JWT Token 是否有效
   - 检查认证信息是否已同步到主进程
   - 验证用户权限

### 调试模式

启用详细日志：

```javascript
// 在 config.js 中
const config = {
  logging: {
    enabled: true,
    level: 'debug'
  }
}
```

## 📝 注意事项

1. **保持原有代码不变**: 尽可能保持原有 JS 代码的结构和逻辑
2. **TypeScript 兼容**: 使用 TypeScript 包装器提供类型安全
3. **认证集成**: 与 Chaterm 的认证系统深度集成
4. **错误处理**: 提供完善的错误处理和降级方案
5. **性能考虑**: 在主进程中缓存密钥，避免重复初始化

## 🤝 贡献

如需修改或扩展功能，请遵循以下原则：

1. 保持原有 JS 代码的完整性
2. 新功能使用 TypeScript 实现
3. 添加完整的错误处理
4. 更新相关文档和示例
5. 进行充分的测试
