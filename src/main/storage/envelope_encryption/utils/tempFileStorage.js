const fs = require('fs').promises;
const path = require('path');

/**
 * 📁 临时文件存储提供者
 * 用于持久化存储会话ID和数据密钥，解决内存存储重启丢失的问题
 */
class TempFileStorageProvider {
  constructor() {
    this.storageDir = path.join(__dirname, '..', '..', 'temp-storage');
    this.ensureStorageDir();
  }

  /**
   * 确保存储目录存在
   */
  async ensureStorageDir() {
    try {
      await fs.access(this.storageDir);
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(this.storageDir, { recursive: true });
      console.log(`📁 创建临时存储目录: ${this.storageDir}`);
    }
  }

  /**
   * 获取文件路径
   */
  getFilePath(key) {
    // 将key中的特殊字符替换为安全字符
    const safeKey = key.replace(/[^a-zA-Z0-9_-]/g, '_');
    return path.join(this.storageDir, `${safeKey}.json`);
  }

  /**
   * 存储数据到文件
   */
  async setItem(key, value) {
    try {
      await this.ensureStorageDir();
      const filePath = this.getFilePath(key);
      await fs.writeFile(filePath, value, 'utf8');
      console.log(`💾 数据已存储到文件: ${key}`);
    } catch (error) {
      console.error(`❌ 存储数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 从文件读取数据
   */
  async getItem(key) {
    try {
      const filePath = this.getFilePath(key);
      const data = await fs.readFile(filePath, 'utf8');
      console.log(`📖 从文件读取数据: ${key}`);
      return data;
    } catch (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在
        return null;
      }
      console.error(`❌ 读取数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 删除文件
   */
  async removeItem(key) {
    try {
      const filePath = this.getFilePath(key);
      await fs.unlink(filePath);
      console.log(`🗑️ 已删除文件: ${key}`);
    } catch (error) {
      if (error.code === 'ENOENT') {
        // 文件不存在，忽略
        return;
      }
      console.error(`❌ 删除文件失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 列出所有匹配前缀的key
   */
  async listKeys(prefix) {
    try {
      await this.ensureStorageDir();
      const files = await fs.readdir(this.storageDir);
      const keys = [];
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          // 移除.json后缀并还原key
          const key = file.slice(0, -5).replace(/_/g, ':');
          if (key.startsWith(prefix)) {
            keys.push(key);
          }
        }
      }
      
      return keys;
    } catch (error) {
      console.error('❌ 列出keys失败:', error);
      return [];
    }
  }

  /**
   * 清理所有存储文件
   */
  async clearAll() {
    try {
      const files = await fs.readdir(this.storageDir);
      for (const file of files) {
        if (file.endsWith('.json')) {
          await fs.unlink(path.join(this.storageDir, file));
        }
      }
      console.log('🧹 已清理所有存储文件');
    } catch (error) {
      console.error('❌ 清理存储失败:', error);
    }
  }
}

module.exports = TempFileStorageProvider;
