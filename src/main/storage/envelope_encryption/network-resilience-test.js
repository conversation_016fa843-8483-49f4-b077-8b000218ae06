/**
 * 🌐 网络容错机制测试
 * 
 * 测试信封加密服务在网络问题时的容错能力
 */

const { envelopeEncryptionService } = require('./service')
const { chatermAuthAdapter } = require('./adapters/chatermAuthAdapter')

/**
 * 模拟网络错误
 */
function createNetworkError(type = 'connection') {
  const errorMessages = {
    connection: 'ECONNREFUSED: Connection refused',
    timeout: 'ETIMEDOUT: Request timeout',
    dns: 'ENOTFOUND: DNS lookup failed',
    reset: 'ECONNRESET: Connection reset by peer',
    fetch: 'fetch failed: network error'
  }
  
  const error = new Error(errorMessages[type] || errorMessages.connection)
  error.code = type.toUpperCase()
  return error
}

/**
 * 测试网络错误检测
 */
async function testNetworkErrorDetection() {
  console.log('\n🔍 === 网络错误检测测试 ===')
  
  try {
    const service = envelopeEncryptionService
    
    // 测试各种网络错误类型
    const errorTypes = ['connection', 'timeout', 'dns', 'reset', 'fetch']
    
    console.log('1. 测试网络错误识别...')
    for (const errorType of errorTypes) {
      const error = createNetworkError(errorType)
      const isNetworkError = service.isNetworkError(error)
      console.log(`   - ${errorType} 错误: ${isNetworkError ? '✅ 识别为网络错误' : '❌ 未识别'}`)
    }
    
    // 测试非网络错误
    console.log('\n2. 测试非网络错误识别...')
    const nonNetworkErrors = [
      new Error('Invalid data format'),
      new Error('Permission denied'),
      new Error('File not found')
    ]
    
    for (const error of nonNetworkErrors) {
      const isNetworkError = service.isNetworkError(error)
      console.log(`   - "${error.message}": ${!isNetworkError ? '✅ 正确识别为非网络错误' : '❌ 误识别为网络错误'}`)
    }
    
    return { success: true }
  } catch (error) {
    console.error('❌ 网络错误检测测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试初始化失败容错
 */
async function testInitializationFailureResilience() {
  console.log('\n🛡️ === 初始化失败容错测试 ===')
  
  try {
    // 设置测试认证信息
    chatermAuthAdapter.setAuthInfo('test-token', 'test-user')
    
    console.log('1. 模拟网络连接失败的初始化...')
    
    // 备份原始的 clientCrypto.initialize 方法
    const originalInitialize = envelopeEncryptionService.clientCrypto.initialize
    
    // 模拟网络失败
    envelopeEncryptionService.clientCrypto.initialize = async () => {
      throw createNetworkError('connection')
    }
    
    // 尝试初始化（应该失败但不抛出错误）
    const initResult = await envelopeEncryptionService.initialize('test-user', true)
    console.log(`   - 初始化结果: ${initResult.success ? '❌ 意外成功' : '✅ 正确失败'}`)
    console.log(`   - 错误信息: ${initResult.message}`)
    
    // 检查服务状态
    const status = envelopeEncryptionService.getStatus()
    console.log(`   - 服务状态: 初始化=${status.initialized}, 失败标记=${envelopeEncryptionService.initializationFailed}`)
    
    // 恢复原始方法
    envelopeEncryptionService.clientCrypto.initialize = originalInitialize
    
    console.log('\n2. 测试服务状态保持...')
    console.log(`   - 主进程继续运行: ✅`)
    console.log(`   - 用户ID已保存: ${envelopeEncryptionService.currentUserId ? '✅' : '❌'}`)
    console.log(`   - 错误信息已记录: ${envelopeEncryptionService.lastInitError ? '✅' : '❌'}`)
    
    return { success: true, initResult, status }
  } catch (error) {
    console.error('❌ 初始化失败容错测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试延迟初始化机制
 */
async function testLazyInitialization() {
  console.log('\n🔄 === 延迟初始化测试 ===')
  
  try {
    // 确保服务处于未初始化状态
    envelopeEncryptionService.isInitialized = false
    envelopeEncryptionService.initializationFailed = true
    envelopeEncryptionService.lastInitError = 'Simulated network error'
    envelopeEncryptionService.currentUserId = 'test-user'
    envelopeEncryptionService.retryCount = 0
    
    console.log('1. 模拟网络恢复后的加密请求...')
    
    // 备份并模拟成功的初始化
    const originalInitialize = envelopeEncryptionService.clientCrypto.initialize
    const originalEncrypt = envelopeEncryptionService.clientCrypto.encryptData
    
    let initializeCalled = false
    envelopeEncryptionService.clientCrypto.initialize = async () => {
      initializeCalled = true
      console.log('   ✅ 重新初始化被调用')
      return Promise.resolve()
    }
    
    envelopeEncryptionService.clientCrypto.encryptData = async (data) => {
      return {
        encrypted: 'mock-encrypted-data',
        algorithm: 'aes-256-gcm',
        keyFingerprint: 'mock-fingerprint',
        userId: 'test-user'
      }
    }
    
    // 尝试加密（应该触发重新初始化）
    try {
      const result = await envelopeEncryptionService.encrypt('test data')
      console.log('   ✅ 加密成功，延迟初始化工作正常')
      console.log(`   - 重新初始化被调用: ${initializeCalled ? '✅' : '❌'}`)
      console.log(`   - 服务状态已恢复: ${envelopeEncryptionService.isInitialized ? '✅' : '❌'}`)
    } catch (error) {
      console.log(`   ❌ 加密失败: ${error.message}`)
    }
    
    // 恢复原始方法
    envelopeEncryptionService.clientCrypto.initialize = originalInitialize
    envelopeEncryptionService.clientCrypto.encryptData = originalEncrypt
    
    return { success: true, initializeCalled }
  } catch (error) {
    console.error('❌ 延迟初始化测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试重试机制
 */
async function testRetryMechanism() {
  console.log('\n🔁 === 重试机制测试 ===')
  
  try {
    // 重置重试计数
    envelopeEncryptionService.retryCount = 0
    envelopeEncryptionService.maxRetries = 3
    envelopeEncryptionService.currentUserId = 'test-user'
    
    console.log('1. 测试重试次数限制...')
    
    // 模拟重试失败
    const originalInitialize = envelopeEncryptionService.clientCrypto.initialize
    let retryAttempts = 0
    
    envelopeEncryptionService.clientCrypto.initialize = async () => {
      retryAttempts++
      throw createNetworkError('timeout')
    }
    
    // 多次尝试重新初始化
    for (let i = 1; i <= 5; i++) {
      const success = await envelopeEncryptionService.tryReinitialize()
      console.log(`   - 第 ${i} 次重试: ${success ? '✅ 成功' : '❌ 失败'}`)
      
      if (envelopeEncryptionService.retryCount >= envelopeEncryptionService.maxRetries) {
        console.log(`   - 已达到最大重试次数 (${envelopeEncryptionService.maxRetries})`)
        break
      }
    }
    
    console.log(`\n2. 重试统计:`)
    console.log(`   - 实际重试次数: ${envelopeEncryptionService.retryCount}`)
    console.log(`   - 最大重试次数: ${envelopeEncryptionService.maxRetries}`)
    console.log(`   - 初始化调用次数: ${retryAttempts}`)
    
    // 恢复原始方法
    envelopeEncryptionService.clientCrypto.initialize = originalInitialize
    
    return { success: true, retryAttempts, maxRetries: envelopeEncryptionService.maxRetries }
  } catch (error) {
    console.error('❌ 重试机制测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行所有网络容错测试
 */
async function runAllNetworkResilienceTests() {
  console.log('🚀 开始网络容错机制测试...')
  console.log('=' .repeat(60))
  
  const results = {
    errorDetection: null,
    initFailure: null,
    lazyInit: null,
    retryMechanism: null
  }
  
  try {
    // 运行所有测试
    results.errorDetection = await testNetworkErrorDetection()
    results.initFailure = await testInitializationFailureResilience()
    results.lazyInit = await testLazyInitialization()
    results.retryMechanism = await testRetryMechanism()
    
    // 输出测试总结
    console.log('\n📊 === 测试总结 ===')
    console.log(`网络错误检测: ${results.errorDetection.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`初始化失败容错: ${results.initFailure.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`延迟初始化: ${results.lazyInit.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`重试机制: ${results.retryMechanism.success ? '✅ 通过' : '❌ 失败'}`)
    
    const allPassed = Object.values(results).every(result => result.success)
    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`)
    
    if (allPassed) {
      console.log('\n🎉 网络容错机制工作正常！')
      console.log('   - 网络错误正确识别')
      console.log('   - 初始化失败不影响主进程')
      console.log('   - 网络恢复后自动重试')
      console.log('   - 重试次数有合理限制')
    }
    
    return { success: allPassed, results }
    
  } catch (error) {
    console.error('❌ 网络容错测试运行失败:', error)
    return { success: false, error: error.message, results }
  } finally {
    // 清理测试环境
    console.log('\n🗑️ 清理测试环境...')
    try {
      envelopeEncryptionService.cleanup(true)
      chatermAuthAdapter.clearAuthInfo()
      console.log('✅ 测试环境清理完成')
    } catch (cleanupError) {
      console.warn('⚠️ 清理测试环境时出错:', cleanupError)
    }
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllNetworkResilienceTests().catch(console.error)
}

module.exports = {
  testNetworkErrorDetection,
  testInitializationFailureResilience,
  testLazyInitialization,
  testRetryMechanism,
  runAllNetworkResilienceTests
}
