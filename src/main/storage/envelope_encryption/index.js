/**
 * 🔐 KMS客户端加密库 - 主入口
 * 
 * 这是客户端加密库的主要导出文件
 * 提供了完整的客户端加密解决方案
 */

const ClientSideCrypto = require('./clientSideCrypto');
const CryptoUtils = require('./utils/crypto');
const StorageManager = require('./utils/storage');
const ApiClient = require('./services/apiClient');
const config = require('./config');

// 主要导出
module.exports = {
  // 🔐 核心加密类
  ClientSideCrypto,
  
  // 🛠️ 工具类
  CryptoUtils,
  StorageManager,
  ApiClient,
  
  // ⚙️ 配置
  config,
  
  // 🚀 便捷创建函数
  createClient: (serverUrl) => new ClientSideCrypto(serverUrl),
  
  // 📋 版本信息
  version: '1.0.0',
  
  // 🛡️ 安全特性说明
  features: [
    '🔐 数据不离开客户端',
    '🔑 密钥分离架构',
    '🛡️ 零信任安全',
    '🔄 密钥轮换支持',
    '💾 安全存储'
  ]
};

// 如果在Node.js环境中直接运行此文件，显示库信息
if (require.main === module) {
  console.log('\n🔐 KMS客户端加密库');
  console.log('=====================================');
  console.log('版本:', module.exports.version);
  console.log('\n🛡️ 安全特性:');
  module.exports.features.forEach(feature => {
    console.log(`  ${feature}`);
  });
  console.log('\n📚 使用示例:');
  console.log('  const { ClientSideCrypto } = require("./src/index");');
  console.log('  const crypto = new ClientSideCrypto("http://localhost:3000");');
  console.log('  await crypto.initialize("user123", "Bearer token");');
  console.log('  const encrypted = crypto.encryptData("sensitive data");');
  console.log('  const decrypted = crypto.decryptData(encrypted);');
  console.log('=====================================\n');
}
