/**
 * 🔐 信封加密服务使用示例
 * 
 * 展示如何在 Chaterm 项目中使用信封加密功能
 */

// 注意：这个文件仅用于演示，实际使用时应该通过 IPC 调用

const { envelopeEncryptionService } = require('./service')

/**
 * 基本加密/解密示例
 */
async function basicEncryptionExample() {
  console.log('\n🔐 === 基本加密/解密示例 ===')
  
  try {
    // 1. 初始化服务（通常在用户登录后自动完成）
    console.log('1. 初始化加密服务...')
    const initResult = await envelopeEncryptionService.initialize('user123')
    if (!initResult.success) {
      throw new Error(initResult.message)
    }
    console.log('✅ 初始化成功')

    // 2. 加密敏感数据
    console.log('\n2. 加密敏感数据...')
    const sensitiveData = 'This is very sensitive information that needs to be encrypted!'
    console.log(`📝 原始数据: ${sensitiveData}`)
    
    const encryptedResult = await envelopeEncryptionService.encrypt(sensitiveData)
    console.log('✅ 加密完成')
    console.log(`🔐 加密数据: ${encryptedResult.encrypted.substring(0, 50)}...`)
    console.log(`🔍 密钥指纹: ${encryptedResult.keyFingerprint}`)
    console.log(`🔧 加密算法: ${encryptedResult.algorithm}`)

    // 3. 解密数据
    console.log('\n3. 解密数据...')
    const decryptedData = await envelopeEncryptionService.decrypt(encryptedResult)
    console.log('✅ 解密完成')
    console.log(`📝 解密数据: ${decryptedData}`)
    
    // 4. 验证数据一致性
    console.log('\n4. 验证数据一致性...')
    if (sensitiveData === decryptedData) {
      console.log('✅ 数据一致性验证通过')
    } else {
      console.log('❌ 数据一致性验证失败')
    }

  } catch (error) {
    console.error('❌ 示例执行失败:', error)
  }
}

/**
 * 批量加密示例
 */
async function batchEncryptionExample() {
  console.log('\n🔐 === 批量加密示例 ===')
  
  try {
    // 模拟多个需要加密的数据
    const dataList = [
      'User password: mySecretPassword123',
      'API Key: sk-1234567890abcdef',
      'Database connection string: ************************:port/db',
      'Private key: -----BEGIN PRIVATE KEY-----...',
      'Session token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
    ]

    console.log(`📦 准备加密 ${dataList.length} 条数据`)

    // 批量加密
    const encryptedResults = []
    for (let i = 0; i < dataList.length; i++) {
      const data = dataList[i]
      console.log(`🔐 加密第 ${i + 1} 条数据...`)
      
      const encrypted = await envelopeEncryptionService.encrypt(data)
      encryptedResults.push(encrypted)
      
      console.log(`✅ 第 ${i + 1} 条数据加密完成`)
    }

    console.log(`✅ 批量加密完成，共处理 ${encryptedResults.length} 条数据`)

    // 批量解密验证
    console.log('\n🔓 验证批量解密...')
    for (let i = 0; i < encryptedResults.length; i++) {
      const decrypted = await envelopeEncryptionService.decrypt(encryptedResults[i])
      if (decrypted === dataList[i]) {
        console.log(`✅ 第 ${i + 1} 条数据解密验证通过`)
      } else {
        console.log(`❌ 第 ${i + 1} 条数据解密验证失败`)
      }
    }

  } catch (error) {
    console.error('❌ 批量加密示例失败:', error)
  }
}

/**
 * 服务状态和健康检查示例
 */
async function statusAndHealthExample() {
  console.log('\n🏥 === 服务状态和健康检查示例 ===')
  
  try {
    // 1. 获取服务状态
    console.log('1. 获取服务状态...')
    const status = envelopeEncryptionService.getStatus()
    console.log('📊 服务状态:')
    console.log(`   - 已初始化: ${status.initialized}`)
    console.log(`   - 用户ID: ${status.userId}`)
    console.log(`   - 密钥指纹: ${status.keyFingerprint}`)
    console.log(`   - 服务器地址: ${status.serverUrl}`)

    // 2. 健康检查
    console.log('\n2. 执行健康检查...')
    const health = await envelopeEncryptionService.healthCheck()
    console.log('🏥 健康检查结果:')
    console.log(`   - 客户端状态: ${health.client?.status || 'unknown'}`)
    console.log(`   - 服务端状态: ${health.server?.status || 'unknown'}`)
    console.log(`   - 认证状态: ${health.authAdapter?.hasToken ? '有效' : '无效'}`)

  } catch (error) {
    console.error('❌ 状态检查示例失败:', error)
  }
}

/**
 * 密钥轮换示例
 */
async function keyRotationExample() {
  console.log('\n🔄 === 密钥轮换示例 ===')
  
  try {
    // 1. 获取当前密钥指纹
    const beforeStatus = envelopeEncryptionService.getStatus()
    console.log(`🔍 轮换前密钥指纹: ${beforeStatus.keyFingerprint}`)

    // 2. 执行密钥轮换
    console.log('🔄 执行密钥轮换...')
    const rotateResult = await envelopeEncryptionService.rotateDataKey()
    
    if (rotateResult.success) {
      console.log('✅ 密钥轮换成功')
      
      // 3. 验证新密钥
      const afterStatus = envelopeEncryptionService.getStatus()
      console.log(`🔍 轮换后密钥指纹: ${afterStatus.keyFingerprint}`)
      
      if (beforeStatus.keyFingerprint !== afterStatus.keyFingerprint) {
        console.log('✅ 密钥指纹已更新')
      } else {
        console.log('⚠️ 密钥指纹未变化')
      }
      
      // 4. 测试新密钥加密
      console.log('🔐 测试新密钥加密...')
      const testData = 'Test data with new key'
      const encrypted = await envelopeEncryptionService.encrypt(testData)
      const decrypted = await envelopeEncryptionService.decrypt(encrypted)
      
      if (testData === decrypted) {
        console.log('✅ 新密钥工作正常')
      } else {
        console.log('❌ 新密钥测试失败')
      }
      
    } else {
      console.log('❌ 密钥轮换失败:', rotateResult.message)
    }

  } catch (error) {
    console.error('❌ 密钥轮换示例失败:', error)
  }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
  console.log('🚀 开始运行信封加密服务示例...')
  
  try {
    await basicEncryptionExample()
    await batchEncryptionExample()
    await statusAndHealthExample()
    await keyRotationExample()
    
    console.log('\n🎉 所有示例运行完成!')
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error)
  } finally {
    // 清理服务
    console.log('\n🗑️ 清理服务...')
    const cleanupResult = envelopeEncryptionService.cleanup(false)
    if (cleanupResult.success) {
      console.log('✅ 服务清理完成')
    } else {
      console.log('❌ 服务清理失败:', cleanupResult.message)
    }
  }
}

// 如果直接运行此文件，执行示例
if (require.main === module) {
  runAllExamples().catch(console.error)
}

module.exports = {
  basicEncryptionExample,
  batchEncryptionExample,
  statusAndHealthExample,
  keyRotationExample,
  runAllExamples
}
