/**
 * 🧪 信封加密服务测试
 *
 * 简单的测试脚本，验证适配是否成功
 */

const { envelopeEncryptionService } = require('./service')
const { chatermAuthAdapter } = require('./adapters/chatermAuthAdapter')

/**
 * 测试认证适配器
 */
async function testAuthAdapter() {
  console.log('\n🔐 === 测试认证适配器 ===')

  try {
    // 1. 测试设置认证信息
    console.log('1. 设置测试认证信息...')
    chatermAuthAdapter.setAuthInfo('test-token-123', 'user-456', Date.now() + 60000)

    // 2. 测试获取认证信息
    console.log('2. 获取认证信息...')
    const token = await chatermAuthAdapter.getAuthToken()
    const userId = await chatermAuthAdapter.getCurrentUserId()

    console.log(`✅ Token: ${token}`)
    console.log(`✅ User ID: ${userId}`)

    // 3. 测试认证状态
    console.log('3. 检查认证状态...')
    const status = chatermAuthAdapter.getAuthStatus()
    console.log('✅ 认证状态:', status)

    return { success: true, token, userId }
  } catch (error) {
    console.error('❌ 认证适配器测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试加密服务初始化
 */
async function testServiceInitialization() {
  console.log('\n🔧 === 测试服务初始化 ===')

  try {
    // 1. 测试服务状态（初始化前）
    console.log('1. 检查初始化前状态...')
    const beforeStatus = envelopeEncryptionService.getStatus()
    console.log('📊 初始化前状态:', beforeStatus)

    // 2. 尝试初始化服务
    console.log('2. 初始化加密服务...')
    const initResult = await envelopeEncryptionService.initialize('test-user-123')
    console.log('✅ 初始化结果:', initResult)

    // 3. 检查初始化后状态
    console.log('3. 检查初始化后状态...')
    const afterStatus = envelopeEncryptionService.getStatus()
    console.log('📊 初始化后状态:', afterStatus)

    return { success: initResult.success, status: afterStatus }
  } catch (error) {
    console.error('❌ 服务初始化测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试基本加密解密功能
 */
async function testBasicEncryption() {
  console.log('\n🔐 === 测试基本加密解密 ===')

  try {
    const testData = 'Hello, this is a test message for encryption!'
    console.log(`📝 测试数据: ${testData}`)

    // 1. 测试加密
    console.log('1. 测试加密...')
    const encrypted = await envelopeEncryptionService.encrypt(testData)
    console.log('✅ 加密成功')
    console.log(`🔐 加密结果: ${JSON.stringify(encrypted, null, 2)}`)

    // 2. 测试解密
    console.log('2. 测试解密...')
    const decrypted = await envelopeEncryptionService.decrypt(encrypted)
    console.log('✅ 解密成功')
    console.log(`📝 解密结果: ${decrypted}`)

    // 3. 验证数据一致性
    console.log('3. 验证数据一致性...')
    const isValid = testData === decrypted
    console.log(`✅ 数据一致性: ${isValid ? '通过' : '失败'}`)

    return { success: isValid, encrypted, decrypted }
  } catch (error) {
    console.error('❌ 基本加密解密测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 测试健康检查
 */
async function testHealthCheck() {
  console.log('\n🏥 === 测试健康检查 ===')

  try {
    console.log('1. 执行健康检查...')
    const health = await envelopeEncryptionService.healthCheck()
    console.log('✅ 健康检查完成')
    console.log(`🏥 健康状态: ${JSON.stringify(health, null, 2)}`)

    return { success: true, health }
  } catch (error) {
    console.error('❌ 健康检查测试失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始运行信封加密服务测试...')
  console.log('='.repeat(50))

  const results = {
    authAdapter: null,
    initialization: null,
    encryption: null,
    healthCheck: null
  }

  try {
    // 1. 测试认证适配器
    results.authAdapter = await testAuthAdapter()

    // 2. 测试服务初始化
    results.initialization = await testServiceInitialization()

    // 3. 如果初始化成功，测试加密功能
    if (results.initialization.success) {
      results.encryption = await testBasicEncryption()
      results.healthCheck = await testHealthCheck()
    } else {
      console.log('⚠️ 服务初始化失败，跳过加密测试')
    }

    // 4. 输出测试总结
    console.log('\n📊 === 测试总结 ===')
    console.log(`认证适配器: ${results.authAdapter.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`服务初始化: ${results.initialization.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`加密解密: ${results.encryption ? (results.encryption.success ? '✅ 通过' : '❌ 失败') : '⏭️ 跳过'}`)
    console.log(`健康检查: ${results.healthCheck ? (results.healthCheck.success ? '✅ 通过' : '❌ 失败') : '⏭️ 跳过'}`)

    const allPassed =
      results.authAdapter.success &&
      results.initialization.success &&
      (results.encryption ? results.encryption.success : true) &&
      (results.healthCheck ? results.healthCheck.success : true)

    console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`)

    return { success: allPassed, results }
  } catch (error) {
    console.error('❌ 测试运行失败:', error)
    return { success: false, error: error.message, results }
  } finally {
    // 清理测试环境
    console.log('\n🗑️ 清理测试环境...')
    try {
      envelopeEncryptionService.cleanup(true)
      chatermAuthAdapter.clearAuthInfo()
      console.log('✅ 测试环境清理完成')
    } catch (cleanupError) {
      console.warn('⚠️ 清理测试环境时出错:', cleanupError)
    }
  }
}

/**
 * 快速验证测试
 */
async function quickTest() {
  console.log('⚡ 快速验证测试...')

  try {
    // 设置测试认证
    chatermAuthAdapter.setAuthInfo('quick-test-token', 'quick-test-user')

    // 检查服务状态
    const status = envelopeEncryptionService.getStatus()
    console.log('📊 服务状态:', status.initialized ? '已初始化' : '未初始化')

    // 检查认证状态
    const authStatus = chatermAuthAdapter.getAuthStatus()
    console.log('🔑 认证状态:', authStatus.hasToken ? '有Token' : '无Token')

    console.log('✅ 快速验证完成')
    return true
  } catch (error) {
    console.error('❌ 快速验证失败:', error)
    return false
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  const args = process.argv.slice(2)

  if (args.includes('--quick')) {
    quickTest().catch(console.error)
  } else {
    runAllTests().catch(console.error)
  }
}

module.exports = {
  testAuthAdapter,
  testServiceInitialization,
  testBasicEncryption,
  testHealthCheck,
  runAllTests,
  quickTest
}
