{"name": "chaterm", "version": "0.2.0", "description": "A terminal tool with AI Agent, makes you no need to learn complicated regular expressions, Perl and Python, switches and Linux commands, SQL syntax can easily manage thousands of devices!", "main": "./out/main/index.js", "author": "chaterm.ai", "homepage": "https://chaterm.ai", "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "lint:staged": "lint-staged", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "dev:watch": "electron-vite dev --watch", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "prepare": "husky", "test": "mocha --require ts-node/register 'src/**/*.test.ts'"}, "lint-staged": {"*.{js,jsx,.cjs,mjs,ts,tsx,cts,mts,vue}": ["prettier --write ", "eslint --fix"]}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@anthropic-ai/bedrock-sdk": "^0.22.0", "@anthropic-ai/sdk": "^0.37.0", "@aws-sdk/client-bedrock-runtime": "^3.812.0", "@aws-sdk/node-http-handler": "^3.374.0", "@bufbuild/protobuf": "^2.2.5", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@types/ssh2": "^1.15.5", "axios": "^1.8.4", "better-sqlite3": "^11.10.0", "clone-deep": "^4.0.1", "crypto-js": "^4.2.0", "diff": "^5.2.0", "dotenv": "^16.5.0", "electron-updater": "^6.6.2", "execa": "^9.5.2", "fast-deep-equal": "^3.1.3", "fzf": "^0.5.2", "get-folder-size": "^5.0.0", "globby": "^14.1.0", "ignore": "^7.0.3", "monaco-editor": "^0.52.2", "openai": "^4.100.0", "p-wait-for": "^3.2.0", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "posthog-node": "^4.8.1", "serialize-error": "^11.0.3", "simple-git": "^3.27.0", "sortablejs": "^1.15.6", "splitpanes": "^4.0.3", "ssh2": "^1.16.0", "strip-ansi": "^7.1.0", "v-contextmenu": "^3.2.0", "vue-i18n": "^9.14.3", "vuedraggable": "^4.1.0", "ws": "^8.18.0", "xterm": "^5.3.0", "xterm-addon-fit": "^0.8.0", "xterm-addon-search": "^0.13.0", "xterm-addon-web-links": "^0.9.0", "xterm-addon-webgl": "^0.16.0", "zod": "^3.24.2", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.7.1", "@types/chai": "^5.0.1", "@types/clone-deep": "^4.0.4", "@types/diff": "^5.2.1", "@types/get-folder-size": "^3.0.4", "@types/jest": "^29.5.14", "@types/marked": "^6.0.0", "@types/mocha": "^10.0.10", "@types/node": "^20.19.0", "@types/should": "^11.2.0", "@types/sinon": "^17.0.4", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.11", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "ant-design-vue": "^4.2.6", "chai": "^4.3.10", "electron": "^30.5.1", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.20.1", "highlight.js": "^11.11.1", "husky": "^9.1.7", "less": "^4.2.2", "lint-staged": "^15.5.1", "marked": "^15.0.7", "mocha": "^10.2.0", "prettier": "^3.2.4", "should": "^13.2.3", "sinon": "^20.0.0", "ts-node": "^10.9.2", "typescript": "^5.8.3", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "uuid": "^11.1.0", "vite": "^6.3.5", "vue": "^3.5.17", "vue-eslint-parser": "^10.1.3", "vue-router": "^4.5.0", "vue-tsc": "^2.2.8"}}